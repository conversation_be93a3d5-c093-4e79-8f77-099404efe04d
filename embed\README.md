# Invoice App - Embeddable Widget

## Quick Start

1. Include the CSS and JavaScript files in your HTML:

```html
<link rel="stylesheet" href="invoice-app.css">
<script src="invoice-app.js"></script>
```

2. Add a container element where you want the invoice app to appear:

```html
<div id="my-invoice-app" class="invoice-app-container"></div>
```

3. The app will automatically initialize! Or manually initialize:

```javascript
const appInstance = InvoiceApp.init('my-invoice-app');
```

## Integration Methods

### Method 1: Automatic Initialization
Add containers with the class `invoice-app-container`:

```html
<div class="invoice-app-container"></div>
```

The app will automatically find and initialize in these containers.

### Method 2: Manual Initialization
```javascript
// Initialize in a specific container
const app = InvoiceApp.init('container-id', {
  // Optional configuration options
});

// Unmount when needed
app.unmount();
```

### Method 3: Multiple Instances
```javascript
// Initialize multiple instances
const instances = InvoiceApp.autoInit({
  // Global options for all instances
});
```

## API Reference

### InvoiceApp.init(containerId, options)
- **containerId**: String - ID of the container element
- **options**: Object - Configuration options (optional)
- **Returns**: Object with `root` and `unmount` methods

### InvoiceApp.autoInit(options)
- **options**: Object - Global configuration options
- **Returns**: Array of app instances

### InvoiceApp.version
- **Returns**: String - Current version of the invoice app

## Styling

The app comes with its own CSS (`invoice-app.css`) but you can override styles:

```css
.invoice-app-container {
    min-height: 600px;
    border: 1px solid #ddd;
    border-radius: 8px;
}
```

## Requirements

- Modern browser with ES6+ support
- Container element with sufficient height (recommended: min-height: 600px)

## File Sizes

- invoice-app.js: ~326KB
- invoice-app.css: ~9KB

## Browser Support

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Example Integration

See `example.html` for a complete working example.
