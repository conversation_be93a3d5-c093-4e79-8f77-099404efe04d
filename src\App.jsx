import React, { Suspense, lazy } from "react";
import ContactSelector from "./components/ContactSelector";
import ProductList from "./components/ProductList";
import InvoiceSummary from "./components/invoice/InvoiceSummary";
import { APP_TITLE, APP_DESCRIPTION } from "./constants";
import "./responsive.css";

// Custom hooks
import { useInvoiceForm } from "./hooks/useInvoiceForm";

// Settings system imports
import { SettingsProvider } from "./contexts/SettingsContext";
import SettingsToolbar from "./components/settings/SettingsToolbar";

// Lazy loaded components for better performance
const InvoicePreview = lazy(() => import("./components/invoice/InvoicePreview"));
const SettingsModal = lazy(() => import("./components/settings/SettingsModal"));

// Main invoice application component
function InvoiceApp() {
  // Use the custom invoice form hook (now inside SettingsProvider)
  const {
    invoiceNumber,
    setInvoiceNumber,
    selectedContact,
    setSelectedContact,
    invoiceItems,
    setInvoiceItems,
    issueDate,
    setIssueDate,
    dueDate,
    setDueDate,
    taxRate,
    setTaxRate,
    discountRate,
    setDiscountRate,
    jobSiteAddress,
    setJobSiteAddress,
    jobSitePhone,
    setJobSitePhone,
    isLoadingInvoiceNumber,
    isSaving,
    saveMessage,
    generateInvoiceNumber,
    handleSaveInvoice,
    handleClearInvoice
  } = useInvoiceForm();

  return (
    <>
      <SettingsToolbar />

      <div className="app-container">
        {/* Left Panel - Invoice Form */}
        <div className="form-panel">
          {/* Header Section */}
          <div style={{ marginBottom: '28px' }}>
            <h1 style={{ fontSize: '24px', fontWeight: '700', color: '#1f2937', marginBottom: '8px' }}>
              {APP_TITLE}
            </h1>
            <p style={{ fontSize: '14px', color: '#6b7280', marginBottom: '16px' }}>
              {APP_DESCRIPTION}
            </p>

            {/* Status Info */}
            <div style={{
              padding: '12px',
              backgroundColor: '#f0f9ff',
              border: '1px solid #0ea5e9',
              borderRadius: '6px',
              fontSize: '12px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <span style={{ color: '#0369a1', fontWeight: '500' }}>
                    📊 Invoice Database
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Invoice Details Section */}
          <div style={{ marginBottom: '28px' }}>
            <h2 style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '12px' }}>
              Invoice Details
            </h2>
            <p style={{ fontSize: '12px', color: '#6b7280', marginBottom: '16px' }}>
              Configure the basic details for this invoice.
            </p>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                Invoice Number
              </label>

              {/* Invoice Prefix Setting */}
              <div style={{ marginBottom: '8px' }}>
                <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                  <button
                    onClick={() => generateInvoiceNumber()}
                    disabled={isLoadingInvoiceNumber}
                    style={{
                      padding: '8px 12px',
                      backgroundColor: isLoadingInvoiceNumber ? '#9ca3af' : '#3b82f6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: '500',
                      cursor: isLoadingInvoiceNumber ? 'not-allowed' : 'pointer',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {isLoadingInvoiceNumber ? '⏳' : '🔄 Generate'}
                  </button>
                </div>
              </div>

              {/* Invoice Number Input Field */}
              <input
                type="text"
                value={isLoadingInvoiceNumber ? 'Generating...' : (invoiceNumber || '')}
                onChange={(e) => setInvoiceNumber(e.target.value)}
                disabled={isLoadingInvoiceNumber}
                placeholder="Invoice number will be generated"
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  border: '1px solid #d1d5db',
                  borderRadius: '6px',
                  fontSize: '14px',
                  boxSizing: 'border-box',
                  backgroundColor: isLoadingInvoiceNumber ? '#f9fafb' : 'white',
                  color: isLoadingInvoiceNumber ? '#6b7280' : '#1f2937'
                }}
              />
            </div>

            <div className="form-row" style={{ display: 'flex', gap: '12px', marginBottom: '12px' }}>
              <div style={{ flex: '1' }}>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Issue Date
                </label>
                <input
                  type="date"
                  value={issueDate}
                  onChange={(e) => setIssueDate(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
              <div style={{ flex: '1' }}>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Due Date
                </label>
                <input
                  type="date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>
          </div>
          {/* Business & Customer Information Section */}
          <div style={{ marginBottom: '28px' }}>
            <h2 style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '12px' }}>
              Business & Customer Information
            </h2>
            <p style={{ fontSize: '12px', color: '#6b7280', marginBottom: '16px' }}>
              Add your business and the customer information to this template.
            </p>

            <div>
              <h3 style={{ fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '12px' }}>
                Customer Information
              </h3>
              <ContactSelector
                onSelect={setSelectedContact}
              />

              {selectedContact && (
                <div style={{
                  padding: '12px',
                  backgroundColor: '#f0f9ff',
                  border: '1px solid #0ea5e9',
                  borderRadius: '6px',
                  fontSize: '14px',
                  marginTop: '8px'
                }}>
                  <strong>Selected:</strong> {selectedContact.firstname} {selectedContact.lastname} ({selectedContact.email})
                  {selectedContact.phone && (
                    <div style={{ fontSize: '12px', color: '#374151', marginTop: '4px' }}>
                      📞 {selectedContact.phone}
                    </div>
                  )}
                  {selectedContact.address && (
                    <div style={{ fontSize: '12px', color: '#374151', marginTop: '4px' }}>
                      📍 {selectedContact.address}
                    </div>
                  )}
                  {selectedContact.company && (
                    <div style={{ fontSize: '12px', color: '#374151', marginTop: '4px' }}>
                      🏢 {selectedContact.company}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Job Site Information Section */}
          <div style={{ marginBottom: '28px' }}>
            <h3 style={{ fontSize: '14px', fontWeight: '500', color: '#374151', marginBottom: '12px' }}>
              Job Site Information (Optional)
            </h3>
            <div className="form-row" style={{ display: 'flex', gap: '12px', marginBottom: '12px' }}>
              <div style={{ flex: '1' }}>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Job Site Address
                </label>
                <input
                  type="text"
                  value={jobSiteAddress}
                  onChange={(e) => setJobSiteAddress(e.target.value)}
                  placeholder="Enter job site address"
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
              <div style={{ flex: '1' }}>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Job Site Phone
                </label>
                <input
                  type="text"
                  value={jobSitePhone}
                  onChange={(e) => setJobSitePhone(e.target.value)}
                  placeholder="Enter job site phone"
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                />
              </div>
            </div>
          </div>

          {/* Products Section */}
          <div style={{ marginBottom: '28px' }}>
            <h2 style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '12px' }}>
              Products & Services
            </h2>
            <p style={{ fontSize: '12px', color: '#6b7280', marginBottom: '16px' }}>
              Add products or services to this invoice.
            </p>

            <ProductList
              items={invoiceItems}
              setItems={setInvoiceItems}
            />
          </div>

          {/* Tax and Discount Section */}
          <div style={{ marginBottom: '28px' }}>
            <h2 style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '12px' }}>
              Tax & Discount
            </h2>
            <p style={{ fontSize: '12px', color: '#6b7280', marginBottom: '16px' }}>
              Apply tax rates and discounts to this invoice.
            </p>

            <div className="form-row" style={{ display: 'flex', gap: '12px' }}>
              <div style={{ flex: '1' }}>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Tax Rate (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                  placeholder="0.00"
                  value={taxRate}
                  onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
                />
              </div>
              <div style={{ flex: '1' }}>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', color: '#374151', marginBottom: '6px' }}>
                  Discount Rate (%)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  style={{
                    width: '100%',
                    padding: '12px 16px',
                    border: '1px solid #d1d5db',
                    borderRadius: '6px',
                    fontSize: '14px',
                    boxSizing: 'border-box'
                  }}
                  placeholder="0.00"
                  value={discountRate}
                  onChange={(e) => setDiscountRate(parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>
          </div>

          {/* Invoice Summary Section */}
          <div style={{ marginBottom: '28px' }}>
            <h2 style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '12px' }}>
              Invoice Summary
            </h2>
            <p style={{ fontSize: '12px', color: '#6b7280', marginBottom: '16px' }}>
              Review the total amounts for this invoice.
            </p>

            <InvoiceSummary
              items={invoiceItems}
              taxRate={taxRate}
              discountRate={discountRate}
            />
          </div>

          {/* Save Invoice Section */}
          <div style={{ marginBottom: '28px' }}>
            <h2 style={{ fontSize: '16px', fontWeight: '600', color: '#1f2937', marginBottom: '12px' }}>
              Save Invoice
            </h2>
            <p style={{ fontSize: '12px', color: '#6b7280', marginBottom: '16px' }}>
              Save this invoice to your database.
            </p>

            <button
              onClick={handleSaveInvoice}
              disabled={isSaving || !selectedContact || invoiceItems.length === 0}
              style={{
                width: '100%',
                padding: '12px 16px',
                backgroundColor: isSaving || !selectedContact || invoiceItems.length === 0 ? '#9ca3af' : '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: isSaving || !selectedContact || invoiceItems.length === 0 ? 'not-allowed' : 'pointer',
                marginBottom: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              {isSaving ? (
                <>
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid #ffffff',
                    borderTop: '2px solid transparent',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                  Saving...
                </>
              ) : (
                <>
                  💾 Save Invoice
                </>
              )}
            </button>

            {saveMessage && (
              <div style={{
                padding: '12px',
                backgroundColor: saveMessage.includes('Error') || saveMessage.includes('❌') ? '#fef2f2' : '#f0f9ff',
                border: `1px solid ${saveMessage.includes('Error') || saveMessage.includes('❌') ? '#fca5a5' : '#0ea5e9'}`,
                borderRadius: '6px',
                fontSize: '14px',
                color: saveMessage.includes('Error') || saveMessage.includes('❌') ? '#dc2626' : '#0369a1'
              }}>
                {saveMessage}
              </div>
            )}
          </div>

          {/* New Invoice Button */}
          <div style={{ marginBottom: '16px' }}>
            <button
              onClick={handleClearInvoice}
              style={{
                width: '100%',
                padding: '12px 16px',
                backgroundColor: '#f3f4f6',
                color: '#374151',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              🆕 New Invoice
            </button>
          </div>
        </div>

        {/* Right Panel - Invoice Preview */}
        <div className="preview-panel">
          <Suspense fallback={<div style={{ padding: '20px', textAlign: 'center' }}>Loading preview...</div>}>
            <InvoicePreview
              invoiceNumber={invoiceNumber}
              issueDate={issueDate}
              dueDate={dueDate}
              contact={selectedContact}
              items={invoiceItems}
              taxRate={taxRate}
              discountRate={discountRate}
              jobSiteAddress={jobSiteAddress}
              jobSitePhone={jobSitePhone}
            />
          </Suspense>
        </div>
      </div>

      {/* Settings Modal */}
      <Suspense fallback={null}>
        <SettingsModal />
      </Suspense>
    </>
  );
}



// Main application component
function App() {
  return (
    <SettingsProvider>
      <InvoiceApp />
    </SettingsProvider>
  );
}

export default App;
