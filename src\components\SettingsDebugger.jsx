import React, { useState } from 'react';
import { testSettingsIntegration, fetchSettings, saveSettingsData } from '../utils/api';

/**
 * Settings Debugger Component
 * Helps debug settings save/load functionality with Google Sheets
 */
const SettingsDebugger = () => {
  const [testResults, setTestResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSettings, setCurrentSettings] = useState(null);

  const runSettingsTest = async () => {
    setIsLoading(true);
    try {
      const results = await testSettingsIntegration();
      setTestResults(results);
    } catch (error) {
      setTestResults({
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCurrentSettings = async () => {
    setIsLoading(true);
    try {
      const settings = await fetchSettings();
      setCurrentSettings(settings);
    } catch (error) {
      setCurrentSettings({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const saveTestSettings = async () => {
    setIsLoading(true);
    try {
      const testSettings = {
        companyName: 'Debug Test Company',
        companyEmail: '<EMAIL>',
        debugTimestamp: new Date().toISOString(),
        testObject: { 
          nested: 'debug value', 
          number: 999,
          boolean: true 
        },
        testArray: ['debug1', 'debug2', 'debug3']
      };
      
      await saveSettingsData(testSettings);
      alert('Test settings saved successfully!');
      
      // Fetch them back immediately
      await fetchCurrentSettings();
    } catch (error) {
      alert(`Error saving test settings: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'white',
      border: '2px solid #ccc',
      borderRadius: '8px',
      padding: '16px',
      maxWidth: '400px',
      maxHeight: '80vh',
      overflow: 'auto',
      zIndex: 9999,
      fontSize: '12px',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
    }}>
      <h3 style={{ margin: '0 0 16px 0', fontSize: '14px' }}>Settings Debugger</h3>
      
      <div style={{ marginBottom: '12px' }}>
        <button 
          onClick={runSettingsTest}
          disabled={isLoading}
          style={{
            padding: '8px 12px',
            marginRight: '8px',
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          {isLoading ? 'Testing...' : 'Run Full Test'}
        </button>
        
        <button 
          onClick={fetchCurrentSettings}
          disabled={isLoading}
          style={{
            padding: '8px 12px',
            marginRight: '8px',
            backgroundColor: '#059669',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          Fetch Settings
        </button>
        
        <button 
          onClick={saveTestSettings}
          disabled={isLoading}
          style={{
            padding: '8px 12px',
            backgroundColor: '#dc2626',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isLoading ? 'not-allowed' : 'pointer'
          }}
        >
          Save Test
        </button>
      </div>

      {testResults && (
        <div style={{ marginBottom: '16px' }}>
          <h4 style={{ margin: '0 0 8px 0', fontSize: '13px' }}>Test Results:</h4>
          <pre style={{
            background: '#f3f4f6',
            padding: '8px',
            borderRadius: '4px',
            fontSize: '10px',
            overflow: 'auto',
            maxHeight: '200px'
          }}>
            {JSON.stringify(testResults, null, 2)}
          </pre>
        </div>
      )}

      {currentSettings && (
        <div>
          <h4 style={{ margin: '0 0 8px 0', fontSize: '13px' }}>Current Settings:</h4>
          <pre style={{
            background: '#f3f4f6',
            padding: '8px',
            borderRadius: '4px',
            fontSize: '10px',
            overflow: 'auto',
            maxHeight: '200px'
          }}>
            {JSON.stringify(currentSettings, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default SettingsDebugger;
