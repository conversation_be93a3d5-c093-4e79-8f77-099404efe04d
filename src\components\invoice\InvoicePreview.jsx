// InvoicePreview component for displaying the final invoice
import React, { useState, useEffect, useRef } from 'react';
import { calculateItemTotal, formatCurrency } from "../../utils/calculations";
import { COMPANY_INFO, INVOICE_SETTINGS } from "../../constants";
import { useSettings } from '../../contexts/SettingsContext';
import QRCodeGenerator from '../QRCodeGenerator';
import carpetMastersLogo from '../../assets/carpet-masters-logo.svg';
import {
  sendInvoiceEmail,
  copyInvoiceEmailToClipboard,
  downloadInvoiceEmail,
  getInvoiceEmailOptions
} from '../../services/emailService';
import './InvoicePreview.css';

export default function InvoicePreview({ contact, items, products = [], invoiceNumber, issueDate, dueDate, taxRate = 0, discountRate = 0, jobSiteAddress = '', jobSitePhone = '' }) {
  // Use settings with fallback for when provider is not available
  let settings;
  try {
    const settingsContext = useSettings();
    settings = settingsContext.settings;
  } catch (error) {
    // Fallback settings if provider is not available
    // console.warn('Settings provider not available, using fallback settings');
    settings = {
      companyName: 'Your Company Name',
      companyAddress: '123 Business Street\nCity, State 12345',
      companyPhone: '(*************',
      companyEmail: '<EMAIL>',
      companyWebsite: 'www.yourcompany.com',
      logoUrl: '',
      logoPlacement: 'left',
      currencySymbol: '$',
      invoiceFooter: 'Thank you for your business!',
      paymentInstructions: 'Please remit payment within 30 days.'
    };
  }

  // Debug: Log settings to console
  // console.log('InvoicePreview settings:', settings);

  // Email state
  const [emailStatus, setEmailStatus] = useState('');
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [showEmailOptions, setShowEmailOptions] = useState(false);
  const emailDropdownRef = useRef(null);

  // Close email dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (emailDropdownRef.current && !emailDropdownRef.current.contains(event.target)) {
        setShowEmailOptions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Custom currency formatter using settings
  const formatCurrencyWithSettings = (amount) => {
    // Safely convert amount to number and format
    const numericAmount = typeof amount === 'number' ? amount : parseFloat(amount || 0);
    const safeAmount = isNaN(numericAmount) ? 0 : numericAmount;
    return `${settings.currencySymbol}${safeAmount.toFixed(2)}`;
  };

  // Get font styles based on settings
  const getFontStyles = (colorType = 'secondary') => {
    const baseFontSize = settings.fontSize === 'small' ? 12 :
                        settings.fontSize === 'medium' ? 14 :
                        settings.fontSize === 'large' ? 16 : 18;

    const fontWeight = settings.fontWeight === 'light' ? '300' :
                      settings.fontWeight === 'normal' ? '400' :
                      settings.fontWeight === 'medium' ? '500' : '700';

    const colorMap = {
      primary: settings.primaryTextColor,
      secondary: settings.secondaryTextColor,
      accent: settings.accentTextColor,
      muted: settings.mutedTextColor
    };

    return {
      fontFamily: settings.fontFamily || 'Arial',
      fontSize: `${baseFontSize}px`,
      fontWeight: fontWeight,
      lineHeight: '1.4',
      color: colorMap[colorType] || settings.secondaryTextColor
    };
  };

  // Get heading font styles (larger than base)
  const getHeadingFontStyles = (multiplier = 1.2, colorType = 'primary') => {
    const baseStyles = getFontStyles(colorType);
    const baseFontSize = parseInt(baseStyles.fontSize);
    return {
      ...baseStyles,
      fontSize: `${Math.round(baseFontSize * multiplier)}px`,
      fontWeight: settings.fontWeight === 'light' ? '400' :
                 settings.fontWeight === 'normal' ? '500' :
                 settings.fontWeight === 'medium' ? '600' : '700'
    };
  };

  // Logo component renderer
  const renderLogo = (customStyle = {}) => {
    const logoStyle = {
      height: '60px',
      width: 'auto',
      maxWidth: '90px',
      ...customStyle
    };

    return (
      <div className="company-logo" style={customStyle.containerStyle || {}}>
        {settings.logoUrl ? (
          <img
            src={settings.logoUrl}
            alt={`${settings.companyName} Logo`}
            style={logoStyle}
            onError={(e) => {
              e.target.style.display = 'none';
            }}
          />
        ) : (
          <img
            src={carpetMastersLogo}
            alt="Default Logo"
            style={logoStyle}
          />
        )}
      </div>
    );
  };

  // Company text component
  const renderCompanyText = (customStyle = {}) => {
    // console.log('Rendering company text with settings:', {
    //   companyName: settings.companyName,
    //   companyAddress: settings.companyAddress,
    //   companyPhone: settings.companyPhone,
    //   companyEmail: settings.companyEmail,
    //   companyWebsite: settings.companyWebsite
    // });

    return (
      <div className="company-text" style={customStyle}>
        <div className="company-name" style={getHeadingFontStyles(1.4, 'primary')}>{settings.companyName || 'No Company Name'}</div>
        <div className="company-details" style={getFontStyles('secondary')}>
          <div style={{ whiteSpace: 'pre-line' }}>{settings.companyAddress || 'No Address'}</div>
          <div>Phone: {settings.companyPhone || 'No Phone'}</div>
          <div>Email: {settings.companyEmail || 'No Email'}</div>
          {settings.companyWebsite && <div>Web: {settings.companyWebsite}</div>}
        </div>
      </div>
    );
  };

  // Calculate totals with custom tax and discount rates
  const subtotal = items.reduce((sum, item) => {
    const product = products.find(p => p.id === item.productId) || { price: item.price || 0 };
    return sum + (product.price * item.quantity);
  }, 0);

  const discountAmount = subtotal * (discountRate / 100);
  const afterDiscount = subtotal - discountAmount;
  const taxAmount = afterDiscount * (taxRate / 100);
  const total = afterDiscount + taxAmount;

  if (!contact || items.length === 0) return null;

  // Use provided values or defaults
  const displayInvoiceNumber = invoiceNumber || Math.floor(Math.random() * 100000);
  const displayIssueDate = issueDate ? new Date(issueDate).toLocaleDateString() : new Date().toLocaleDateString();
  const displayDueDate = dueDate ? new Date(dueDate).toLocaleDateString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString();

  const handlePrint = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    const invoiceContent = document.querySelector('.invoice-container').outerHTML;

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Invoice ${displayInvoiceNumber}</title>
          <style>
            @page {
              size: letter;
              margin: 0.5in;
              /* Remove browser headers and footers */
              margin-top: 0.5in;
              margin-bottom: 0.5in;
              @top-left { content: ""; }
              @top-center { content: ""; }
              @top-right { content: ""; }
              @bottom-left { content: ""; }
              @bottom-center { content: ""; }
              @bottom-right { content: ""; }
            }

            * {
              -webkit-print-color-adjust: exact !important;
              print-color-adjust: exact !important;
              box-sizing: border-box;
            }

            body {
              margin: 0;
              padding: 0;
              background: white;
              font-family: Arial, sans-serif;
              font-size: 12px;
              line-height: 1.3;
              color: black;
            }

            .invoice-container {
              width: 100%;
              max-width: none;
              margin: 0;
              padding: 0;
              background: white;
              min-height: 100vh;
              display: flex;
              flex-direction: column;
            }

            .invoice-content {
              flex: 1;
              display: flex;
              flex-direction: column;
            }

            .invoice-footer {
              margin-top: auto;
              flex-shrink: 0;
              text-align: center;
              padding-top: 1rem;
              border-top: 1px solid #d1d5db;
            }

            .invoice-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 1rem;
            }

            .company-info {
              flex: 1;
            }

            .company-info-content {
              display: flex;
              align-items: flex-start;
              gap: 1rem;
            }

            .company-text {
              flex: 1;
            }

            .company-logo {
              flex-shrink: 0;
            }

            .company-logo img {
              height: 60px;
              width: auto;
              max-width: 90px;
            }

            .company-name {
              font-size: 18px;
              font-weight: bold;
              border-bottom: 2px solid black;
              padding-bottom: 0.25rem;
              margin-bottom: 0.5rem;
            }

            .company-details {
              font-size: 10px;
              line-height: 1.2;
              margin-top: 0.25rem;
            }

            .invoice-title {
              text-align: right;
            }

            .invoice-title h2 {
              font-size: 24px;
              font-weight: bold;
              margin: 0 0 0.5rem 0;
            }

            .invoice-details {
              font-size: 10px;
            }

            .invoice-details > div {
              margin-bottom: 0.25rem;
            }

            .billing-section {
              display: flex;
              justify-content: space-between;
              margin-bottom: 1rem;
            }

            .bill-to, .job-site {
              flex: 1;
            }

            .bill-to h3, .job-site h3 {
              font-weight: bold;
              font-size: 12px;
              margin-bottom: 0.25rem;
            }

            .job-site {
              text-align: right;
            }

            .items-table {
              width: 100%;
              font-size: 10px;
              margin-bottom: 1rem;
              border-collapse: collapse;
            }

            .items-table thead tr {
              border-bottom: 2px solid black;
            }

            .items-table th {
              padding: 0.25rem 0.25rem 0.5rem 0.25rem;
              font-weight: bold;
              text-align: left;
            }

            .items-table tbody tr {
              border-bottom: 1px solid #d1d5db;
            }

            .items-table td {
              padding: 0.25rem;
            }

            .totals-section {
              border-top: 2px solid black;
              padding-top: 0.5rem;
              display: flex;
              justify-content: space-between;
              margin-top: 1rem;
            }

            .terms-conditions {
              flex: 1;
              margin-right: 2rem;
              font-size: 9px;
              line-height: 1.2;
            }

            .totals-box {
              min-width: 200px;
              font-size: 10px;
            }

            .totals-header {
              display: flex;
              justify-content: space-between;
              margin-bottom: 0.5rem;
            }

            .totals-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 0.25rem;
            }

            .totals-final {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              font-weight: bold;
              border-top: 1px solid black;
              padding-top: 0.25rem;
              margin-top: 0.5rem;
            }

            .footer-info {
              margin-top: 1rem;
              font-size: 9px;
              text-align: center;
              border-top: 1px solid #ccc;
              padding-top: 0.5rem;
            }

            .business-services {
              margin-bottom: 1rem;
              padding: 1rem;
              font-size: 9px;
              line-height: 1.2;
              text-align: center;
              color: #374151;
              border-bottom: 1px solid #d1d5db;
            }

            .business-services p {
              margin: 0 0 0.25rem 0;
            }

            .business-services p:last-child {
              margin-bottom: 0;
            }
          </style>
        </head>
        <body>
          ${invoiceContent}
        </body>
      </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    // Wait for content to load then print
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  // Email handling functions
  const handleSendEmail = async () => {
    if (!contact?.email) {
      setEmailStatus('Customer email address is not available');
      return;
    }

    setIsEmailLoading(true);
    setEmailStatus('');

    try {
      // Create invoice data object
      const invoiceData = {
        invoiceNumber: displayInvoiceNumber,
        customerName: `${contact.firstname} ${contact.lastname}`,
        customerEmail: contact.email,
        customerAddress: contact.address,
        customerPhone: contact.phone,
        issueDate: displayIssueDate,
        dueDate: displayDueDate,
        total: total,
        subtotal: subtotal,
        jobSiteAddress: jobSiteAddress,
        jobSitePhone: jobSitePhone,
        items: items.map(item => ({
          productName: getProductName(item.productId),
          description: item.description || item.notes,
          quantity: item.quantity,
          unit: item.unit,
          price: item.price,
          total: calculateItemTotal(item)
        }))
      };

      // Get invoice HTML content
      const invoiceHTML = document.querySelector('.invoice-container').outerHTML;

      // Send email
      const result = sendInvoiceEmail(invoiceData, settings, invoiceHTML);

      if (result.success) {
        setEmailStatus(`✅ ${result.message}`);
      } else {
        setEmailStatus(`❌ ${result.message}`);
      }
    } catch (error) {
      // console.error('Error sending email:', error);
      setEmailStatus('❌ Failed to send email');
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleCopyEmailContent = async () => {
    setIsEmailLoading(true);
    setEmailStatus('');

    try {
      const invoiceData = {
        invoiceNumber: displayInvoiceNumber,
        customerName: `${contact.firstname} ${contact.lastname}`,
        customerEmail: contact.email,
        issueDate: displayIssueDate,
        dueDate: displayDueDate,
        total: total,
        jobSiteAddress: jobSiteAddress,
        jobSitePhone: jobSitePhone
      };

      const invoiceHTML = document.querySelector('.invoice-container').outerHTML;
      const result = await copyInvoiceEmailToClipboard(invoiceData, settings, invoiceHTML);

      if (result.success) {
        setEmailStatus(`✅ ${result.message}`);
      } else {
        setEmailStatus(`❌ ${result.message}`);
      }
    } catch (error) {
      // console.error('Error copying email content:', error);
      setEmailStatus('❌ Failed to copy email content');
    } finally {
      setIsEmailLoading(false);
    }
  };

  const handleDownloadEmail = () => {
    setEmailStatus('');

    try {
      const invoiceData = {
        invoiceNumber: displayInvoiceNumber,
        customerName: `${contact.firstname} ${contact.lastname}`,
        customerEmail: contact.email,
        issueDate: displayIssueDate,
        dueDate: displayDueDate,
        total: total,
        jobSiteAddress: jobSiteAddress,
        jobSitePhone: jobSitePhone
      };

      const invoiceHTML = document.querySelector('.invoice-container').outerHTML;
      const result = downloadInvoiceEmail(invoiceData, settings, invoiceHTML);

      if (result.success) {
        setEmailStatus(`✅ ${result.message}`);
      } else {
        setEmailStatus(`❌ ${result.message}`);
      }
    } catch (error) {
      // console.error('Error downloading email:', error);
      setEmailStatus('❌ Failed to download email');
    }
  };

  // Helper function to get product name from product ID
  const getProductName = (productId) => {
    const product = products.find(p => p.id === productId);
    return product ? product.name : productId;
  };

  return (
    <div>
      {/* Action Buttons */}
      <div style={{textAlign: 'right', marginBottom: '1rem', display: 'flex', gap: '8px', justifyContent: 'flex-end'}} className="print:hidden no-print">
        {/* Email Button */}
        <div ref={emailDropdownRef} style={{ position: 'relative' }}>
          <button
            onClick={() => setShowEmailOptions(!showEmailOptions)}
            style={{
              backgroundColor: contact?.email ? '#059669' : '#6b7280',
              color: 'white',
              border: 'none',
              padding: '8px 16px',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: contact?.email ? 'pointer' : 'not-allowed',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '8px',
              opacity: isEmailLoading ? 0.7 : 1
            }}
            disabled={isEmailLoading}
            title={contact?.email ? `Email invoice to ${contact.email}` : 'Customer email not available'}
          >
            {isEmailLoading ? '⏳' : '📧'} Email Invoice
          </button>

          {/* Email Options Dropdown */}
          {showEmailOptions && (
            <div style={{
              position: 'absolute',
              top: '100%',
              right: 0,
              marginTop: '4px',
              backgroundColor: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              zIndex: 1000,
              minWidth: '200px'
            }}>
              <div style={{ padding: '8px' }}>
                <div style={{
                  fontSize: '12px',
                  color: '#6b7280',
                  marginBottom: '8px',
                  fontWeight: '500'
                }}>
                  Email Options:
                </div>

                {contact?.email ? (
                  <>
                    <button
                      onClick={handleSendEmail}
                      disabled={isEmailLoading}
                      style={{
                        width: '100%',
                        padding: '8px 12px',
                        backgroundColor: '#059669',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        marginBottom: '4px',
                        textAlign: 'left'
                      }}
                    >
                      📧 Send to {contact.email}
                    </button>

                    <button
                      onClick={handleCopyEmailContent}
                      disabled={isEmailLoading}
                      style={{
                        width: '100%',
                        padding: '8px 12px',
                        backgroundColor: '#3b82f6',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        marginBottom: '4px',
                        textAlign: 'left'
                      }}
                    >
                      📋 Copy Email Content
                    </button>

                    <button
                      onClick={handleDownloadEmail}
                      disabled={isEmailLoading}
                      style={{
                        width: '100%',
                        padding: '8px 12px',
                        backgroundColor: '#8b5cf6',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '12px',
                        cursor: 'pointer',
                        textAlign: 'left'
                      }}
                    >
                      💾 Download Email HTML
                    </button>
                  </>
                ) : (
                  <div style={{
                    padding: '8px 12px',
                    fontSize: '12px',
                    color: '#6b7280',
                    textAlign: 'center'
                  }}>
                    Customer email not available
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Print Button */}
        <button
          onClick={handlePrint}
          className="print-button"
          style={{
            backgroundColor: '#2563eb',
            color: 'white',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '6px',
            fontSize: '14px',
            cursor: 'pointer',
            display: 'inline-flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          🖨️ Print Invoice
        </button>
      </div>

      {/* Email Status Message */}
      {emailStatus && (
        <div style={{
          textAlign: 'right',
          marginBottom: '1rem',
          padding: '8px 12px',
          backgroundColor: emailStatus.includes('✅') ? '#f0fdf4' : '#fef2f2',
          color: emailStatus.includes('✅') ? '#166534' : '#dc2626',
          border: `1px solid ${emailStatus.includes('✅') ? '#bbf7d0' : '#fecaca'}`,
          borderRadius: '6px',
          fontSize: '12px'
        }} className="print:hidden no-print">
          {emailStatus}
        </div>
      )}

      <div className="invoice-container" style={getFontStyles()}>
      <div className="invoice-content">
      {/* Header Section */}
      <div className="invoice-header">
        {/* Company Info - Dynamic Logo Placement */}
        <div className="company-info">
          {settings.logoPlacement === 'left' && (
            <div className="company-info-content">
              {renderLogo()}
              {renderCompanyText()}
            </div>
          )}

          {settings.logoPlacement === 'right' && (
            <div className="company-info-content">
              {renderCompanyText()}
              {renderLogo()}
            </div>
          )}

          {settings.logoPlacement === 'above-center' && (
            <div>
              <div style={{ textAlign: 'center', marginBottom: '1rem' }}>
                {renderLogo({ containerStyle: { marginBottom: '0.5rem' } })}
              </div>
              {renderCompanyText({ textAlign: 'center' })}
            </div>
          )}

          {settings.logoPlacement === 'above-left' && (
            <div>
              {renderLogo({ containerStyle: { marginBottom: '0.5rem' } })}
              {renderCompanyText()}
            </div>
          )}

          {settings.logoPlacement === 'large-left' && (
            <div className="company-info-content">
              {renderLogo({
                height: '80px',
                maxWidth: '120px',
                containerStyle: { marginRight: '1.5rem' }
              })}
              {renderCompanyText()}
            </div>
          )}

          {settings.logoPlacement === 'background' && (
            <div style={{ position: 'relative' }}>
              {renderLogo({
                height: '100px',
                containerStyle: {
                  position: 'absolute',
                  right: '0',
                  top: '0',
                  opacity: '0.1',
                  zIndex: '0'
                }
              })}
              {renderCompanyText({ position: 'relative', zIndex: '1' })}
            </div>
          )}
        </div>

        {/* Invoice Title and Details */}
        <div className="invoice-title">
          <h2 style={getHeadingFontStyles(2, 'primary')}>INVOICE</h2>
          <div className="invoice-details" style={getFontStyles('secondary')}>
            <div><strong>Invoice #:</strong> {displayInvoiceNumber}</div>
            <div><strong>Issue Date:</strong> {displayIssueDate}</div>
            <div><strong>Due Date:</strong> {displayDueDate}</div>
          </div>
        </div>
      </div>

      {/* Bill To Section */}
      <div className="billing-section" style={getFontStyles()}>
        <div className="bill-to">
          <h3 style={getHeadingFontStyles(1.1)}>Bill To:</h3>
          <div>
            <div style={{fontWeight: 'bold'}}>{contact.firstname} {contact.lastname}</div>
            <div>{contact.email}</div>
            {contact.address && <div>{contact.address}</div>}
            {contact.city && contact.state && (
              <div>{contact.city}, {contact.state} {contact.zip}</div>
            )}
          </div>
        </div>

        <div className="job-site">
          <h3 style={getHeadingFontStyles(1.1)}>Job Site:</h3>
          <div>
            {jobSiteAddress ? (
              <div style={{ whiteSpace: 'pre-line' }}>{jobSiteAddress}</div>
            ) : (
              <div style={{ color: '#6b7280', fontStyle: 'italic' }}>
                No job site address provided
              </div>
            )}
            {jobSitePhone && (
              <div style={{ marginTop: '8px' }}>
                <strong>Phone:</strong> {jobSitePhone}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Separator line below billing section */}
      <div style={{
        borderBottom: '2px solid black',
        marginBottom: '0.75rem'
      }}></div>

      {/* Items Table */}
      <table className="items-table" style={getFontStyles()}>
        <thead>
          <tr>
            <th style={{textAlign: 'left', ...getHeadingFontStyles(1)}}>Room / Item</th>
            <th style={{textAlign: 'left', ...getHeadingFontStyles(1)}}>Description</th>
            <th style={{textAlign: 'center', ...getHeadingFontStyles(1)}}>Quantity</th>
            <th style={{textAlign: 'right', ...getHeadingFontStyles(1)}}>Price</th>
            <th style={{textAlign: 'right', ...getHeadingFontStyles(1)}}>Amount</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item, i) => {
            const productName = getProductName(item.productId);
            const itemTotal = calculateItemTotal(item);

            // Combine catalog description and notes
            const getFullDescription = () => {
              const catalogDesc = item.description || productName;
              const notes = item.notes;

              if (catalogDesc && notes) {
                return (
                  <div>
                    <div>{catalogDesc}</div>
                    {notes && (
                      <div style={{
                        fontSize: '0.9em',
                        color: '#6b7280',
                        fontStyle: 'italic',
                        marginTop: '4px'
                      }}>
                        {notes}
                      </div>
                    )}
                  </div>
                );
              } else if (notes) {
                return notes;
              } else {
                return catalogDesc;
              }
            };

            return (
              <tr key={i}>
                <td>{productName}</td>
                <td>{getFullDescription()}</td>
                <td style={{textAlign: 'center'}}>{item.quantity} {item.unit}</td>
                <td style={{textAlign: 'right'}}>{formatCurrencyWithSettings(item?.unitPrice || item?.price || 0)}</td>
                <td style={{textAlign: 'right'}}>{formatCurrencyWithSettings(itemTotal)}</td>
              </tr>
            );
          })}
        </tbody>
      </table>

      {/* Totals Section */}
      <div className="totals-section" style={getFontStyles()}>
        {/* Left side - Terms and conditions */}
        <div className="terms-conditions">
          <p style={getHeadingFontStyles(1)}><strong>Terms & Conditions:</strong></p>
          <p>Payment is due within {INVOICE_SETTINGS.paymentTerms.toLowerCase()} of invoice date. Late payments may be subject to interest charges.
          All work performed according to industry standards and manufacturer specifications.</p>
        </div>

        {/* Right side - Totals */}
        <div className="totals-box" style={getFontStyles()}>
          <div className="totals-header">
            <span style={{fontWeight: 'bold', ...getFontStyles('primary')}}>Subtotal</span>
            <span style={{fontWeight: 'bold', ...getFontStyles('accent')}}>Total</span>
          </div>

          <div className="totals-row">
            <span></span>
            <span style={{fontWeight: 'bold'}}>{formatCurrencyWithSettings(subtotal)}</span>
          </div>

          {discountRate > 0 && (
            <div className="totals-row">
              <span>Discount ({discountRate}%):</span>
              <span style={{color: '#ef4444'}}>-{formatCurrencyWithSettings(discountAmount)}</span>
            </div>
          )}

          {taxRate > 0 && (
            <div className="totals-row">
              <span>Tax ({taxRate}%):</span>
              <span>{formatCurrencyWithSettings(taxAmount)}</span>
            </div>
          )}

          <div className="totals-row">
            <span style={{fontWeight: 'bold'}}>Payment or Credits:</span>
            <span>{formatCurrencyWithSettings(0)}</span>
          </div>

          <div className="totals-final">
            <span style={{...getFontStyles('primary'), fontWeight: 'bold'}}>Balance:</span>
            <span style={{...getFontStyles('accent'), fontWeight: 'bold', fontSize: '18px'}}>{formatCurrencyWithSettings(total)}</span>
          </div>
        </div>
      </div>

      </div>

      {/* Payment Options Section */}
      {(settings.showPayNowButton || settings.showQRCode) && (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: settings.paymentSectionSize === 'small' ? '12px' :
               settings.paymentSectionSize === 'medium' ? '20px' : '28px',
          margin: settings.paymentSectionSize === 'small' ? '20px 0' :
                  settings.paymentSectionSize === 'medium' ? '40px 0' : '50px 0',
          padding: settings.paymentSectionSize === 'small' ? '16px' :
                   settings.paymentSectionSize === 'medium' ? '20px' : '28px',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{ textAlign: 'center' }}>
            <h3 style={{
              margin: 0,
              fontSize: settings.paymentSectionSize === 'small' ? '16px' :
                        settings.paymentSectionSize === 'medium' ? '18px' : '22px',
              fontWeight: '600',
              color: '#1f2937',
              ...getHeadingFontStyles(settings.paymentSectionSize === 'small' ? 1.1 :
                                     settings.paymentSectionSize === 'medium' ? 1.3 : 1.5)
            }}>
              Payment Options
            </h3>
            {settings.showAmountInPayment && (
              <div style={{
                marginTop: '8px',
                fontSize: settings.paymentSectionSize === 'small' ? '20px' :
                          settings.paymentSectionSize === 'medium' ? '24px' : '28px',
                fontWeight: '700',
                ...getHeadingFontStyles(settings.paymentSectionSize === 'small' ? 1.4 :
                                       settings.paymentSectionSize === 'medium' ? 1.7 : 2, 'accent')
              }}>
                Amount Due: {formatCurrencyWithSettings(total)}
              </div>
            )}
          </div>

          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: settings.paymentSectionSize === 'small' ? '20px' :
                 settings.paymentSectionSize === 'medium' ? '30px' : '40px',
            flexWrap: 'wrap',
            justifyContent: 'center'
          }}>
            {/* Pay Now Button */}
            {settings.showPayNowButton && (
              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
                <a
                  href={settings.payNowButtonUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    display: 'inline-block',
                    padding: settings.paymentSectionSize === 'small' ? '10px 20px' :
                             settings.paymentSectionSize === 'medium' ? '12px 24px' : '16px 32px',
                    backgroundColor: settings.payNowButtonColor,
                    color: 'white',
                    textDecoration: 'none',
                    borderRadius: '6px',
                    fontSize: settings.paymentSectionSize === 'small' ? '14px' :
                              settings.paymentSectionSize === 'medium' ? '16px' : '18px',
                    fontWeight: '600',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    ...getFontStyles()
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.transform = 'translateY(-1px)';
                    e.target.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                  }}
                >
                  Pay Now
                </a>
                <p style={{
                  fontSize: settings.paymentSectionSize === 'small' ? '11px' : '12px',
                  color: '#6b7280',
                  margin: 0,
                  textAlign: 'center'
                }}>
                  Click to pay online
                </p>
              </div>
            )}

            {/* QR Code */}
            {settings.showQRCode && (
              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
                <QRCodeGenerator
                  data={settings.qrCodeData}
                  size={settings.paymentSectionSize === 'small' ? Math.min(settings.qrCodeSize, 100) :
                        settings.paymentSectionSize === 'medium' ? settings.qrCodeSize :
                        Math.max(settings.qrCodeSize, 140)}
                  alt="Payment QR Code"
                />
              </div>
            )}
          </div>

          {/* Payment Instructions */}
          <div style={{
            textAlign: 'center',
            fontSize: settings.paymentSectionSize === 'small' ? '11px' :
                      settings.paymentSectionSize === 'medium' ? '12px' : '14px',
            color: '#6b7280',
            maxWidth: settings.paymentSectionSize === 'small' ? '300px' :
                     settings.paymentSectionSize === 'medium' ? '400px' : '500px',
            ...getFontStyles()
          }}>
            {settings.showPayNowButton && settings.showQRCode ? (
              <p style={{ margin: 0 }}>
                Pay online using the button above or scan the QR code with your mobile device
              </p>
            ) : settings.showPayNowButton ? (
              <p style={{ margin: 0 }}>
                Click the button above to pay online securely
              </p>
            ) : (
              <p style={{ margin: 0 }}>
                Scan the QR code with your mobile device to pay
              </p>
            )}
          </div>
        </div>
      )}

      {/* Footer with Business Services */}
      <div className="invoice-footer" style={getFontStyles()}>
        <div className="business-services">
          {settings.invoiceFooter && (
            <p style={{ fontWeight: 'bold', marginBottom: '8px', ...getHeadingFontStyles(1) }}>
              {settings.invoiceFooter}
            </p>
          )}
          {settings.paymentInstructions && (
            <p style={{ fontSize: '12px', color: '#6b7280', marginBottom: '8px' }}>
              {settings.paymentInstructions}
            </p>
          )}
          <p style={{ fontSize: '10px', color: '#9ca3af' }}>
            All work performed according to industry standards and manufacturer specifications.
          </p>
        </div>
      </div>
      </div>
    </div>
  );
}
