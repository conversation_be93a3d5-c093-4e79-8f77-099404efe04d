// Settings modal component
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { useSettings } from '../../contexts/SettingsContext';
import { ModalPropTypes, SettingsPropTypes } from '../../types';

const SettingsModal = () => {
  const {
    settings,
    updateSetting,
    updateSettings,
    resetSettings,
    exportSettings,
    importSettings,
    isSettingsOpen,
    setIsSettingsOpen,
    isSettingsLoading,
    settingsError
  } = useSettings();

  const [activeTab, setActiveTab] = useState('company');
  const [importMessage, setImportMessage] = useState('');
  const [saveMessage, setSaveMessage] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  if (!isSettingsOpen) return null;

  const handleFileImport = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      await importSettings(file);
      setImportMessage('✅ Settings imported successfully!');
      setTimeout(() => setImportMessage(''), 3000);
    } catch (error) {
      setImportMessage('❌ Failed to import settings: ' + error.message);
      setTimeout(() => setImportMessage(''), 5000);
    }
    event.target.value = ''; // Reset file input
  };

  const handleSaveSettings = async () => {
    setIsSaving(true);
    setSaveMessage('');

    try {
      // Manually save settings only when Save button is clicked
      await updateSettings(settings);
      setSaveMessage('✅ Settings saved successfully!');
      setTimeout(() => {
        setSaveMessage('');
        setIsSettingsOpen(false); // Close modal after successful save
      }, 1500);
    } catch (error) {
      console.error('Error saving settings:', error);
      setSaveMessage('❌ Failed to save settings: ' + error.message);
      setTimeout(() => setSaveMessage(''), 5000);
    } finally {
      setIsSaving(false);
    }
  };

  const tabs = [
    { id: 'company', label: '🏢 Company', icon: '🏢' },
    { id: 'branding', label: '🎨 Branding', icon: '🎨' },
    { id: 'typography', label: '🔤 Typography', icon: '🔤' },
    { id: 'invoice', label: '📄 Invoice', icon: '📄' },
    { id: 'payment', label: '💳 Payment', icon: '💳' },
    { id: 'paymentoptions', label: '💰 Payment Options', icon: '💰' },
    { id: 'integration', label: '🔗 Integration', icon: '🔗' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'company':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                Company Name
              </label>
              <input
                type="text"
                value={settings.companyName}
                onChange={(e) => updateSetting('companyName', e.target.value)}
                style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
              />
            </div>

            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                Company Address
              </label>
              <textarea
                value={settings.companyAddress}
                onChange={(e) => updateSetting('companyAddress', e.target.value)}
                rows={3}
                style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px', resize: 'vertical' }}
              />
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={settings.companyPhone}
                  onChange={(e) => updateSetting('companyPhone', e.target.value)}
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                />
              </div>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Email Address
                </label>
                <input
                  type="email"
                  value={settings.companyEmail}
                  onChange={(e) => updateSetting('companyEmail', e.target.value)}
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                />
              </div>
            </div>

            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                Website
              </label>
              <input
                type="url"
                value={settings.companyWebsite}
                onChange={(e) => updateSetting('companyWebsite', e.target.value)}
                placeholder="www.yourcompany.com"
                style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
              />
            </div>
          </div>
        );

      case 'branding':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                Logo Source
              </label>

              {/* Logo input method tabs */}
              <div style={{ display: 'flex', gap: '4px', marginBottom: '8px' }}>
                <button
                  type="button"
                  onClick={() => updateSetting('logoInputMethod', 'url')}
                  style={{
                    padding: '6px 12px',
                    backgroundColor: (settings.logoInputMethod || 'url') === 'url' ? '#3b82f6' : '#f3f4f6',
                    color: (settings.logoInputMethod || 'url') === 'url' ? 'white' : '#6b7280',
                    border: 'none',
                    borderRadius: '4px',
                    fontSize: '12px',
                    cursor: 'pointer'
                  }}
                >
                  🔗 URL
                </button>
                <button
                  type="button"
                  onClick={() => updateSetting('logoInputMethod', 'file')}
                  style={{
                    padding: '6px 12px',
                    backgroundColor: settings.logoInputMethod === 'file' ? '#3b82f6' : '#f3f4f6',
                    color: settings.logoInputMethod === 'file' ? 'white' : '#6b7280',
                    border: 'none',
                    borderRadius: '4px',
                    fontSize: '12px',
                    cursor: 'pointer'
                  }}
                >
                  📁 File Upload
                </button>
              </div>

              {/* URL Input */}
              {(settings.logoInputMethod || 'url') === 'url' && (
                <div>
                  <input
                    type="url"
                    value={settings.logoUrl || ''}
                    onChange={(e) => updateSetting('logoUrl', e.target.value)}
                    placeholder="https://example.com/logo.png"
                    style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                  />
                  <p style={{ fontSize: '12px', color: '#6b7280', margin: '4px 0 0 0' }}>
                    Enter a direct link to your logo image (PNG, JPG, SVG)
                  </p>
                </div>
              )}

              {/* File Upload */}
              {settings.logoInputMethod === 'file' && (
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files[0];
                      if (file) {
                        const reader = new FileReader();
                        reader.onload = (event) => {
                          updateSetting('logoUrl', event.target.result);
                        };
                        reader.readAsDataURL(file);
                      }
                    }}
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      border: '1px solid #d1d5db',
                      borderRadius: '6px',
                      fontSize: '14px',
                      backgroundColor: 'white'
                    }}
                  />
                  <p style={{ fontSize: '12px', color: '#6b7280', margin: '4px 0 0 0' }}>
                    Upload an image file from your computer (PNG, JPG, SVG, etc.)
                  </p>
                </div>
              )}
            </div>

            {settings.logoUrl && (
              <div style={{ padding: '12px', backgroundColor: '#f9fafb', borderRadius: '6px', textAlign: 'center' }}>
                <p style={{ fontSize: '12px', color: '#6b7280', margin: '0 0 8px 0' }}>Logo Preview:</p>
                <img 
                  src={settings.logoUrl} 
                  alt="Logo Preview"
                  style={{ maxWidth: '200px', maxHeight: '80px', objectFit: 'contain' }}
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
                <p style={{ fontSize: '12px', color: '#dc2626', margin: '8px 0 0 0', display: 'none' }}>
                  Failed to load logo. Please check the URL.
                </p>
              </div>
            )}

            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                Logo Placement
              </label>
              <select
                value={settings.logoPlacement}
                onChange={(e) => updateSetting('logoPlacement', e.target.value)}
                style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px', marginBottom: '16px' }}
              >
                <option value="left">📍 Left of Company Info</option>
                <option value="right">📍 Right of Company Info</option>
                <option value="above-center">📍 Above Company Info (Centered)</option>
                <option value="above-left">📍 Above Company Info (Left-aligned)</option>
                <option value="large-left">📍 Large Logo Left</option>
                <option value="background">📍 Background Watermark</option>
              </select>
              <p style={{ fontSize: '12px', color: '#6b7280', margin: '4px 0 8px 0' }}>
                Choose where to position your logo on invoices
              </p>

              {/* Logo Placement Preview */}
              <div style={{
                padding: '12px',
                backgroundColor: '#f9fafb',
                borderRadius: '6px',
                border: '1px solid #e5e7eb',
                fontSize: '11px',
                fontFamily: 'monospace'
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '12px', fontFamily: 'inherit' }}>
                  Preview: {settings.logoPlacement === 'left' ? '📍 Logo Left' :
                           settings.logoPlacement === 'right' ? '📍 Logo Right' :
                           settings.logoPlacement === 'above-center' ? '📍 Logo Above (Center)' :
                           settings.logoPlacement === 'above-left' ? '📍 Logo Above (Left)' :
                           settings.logoPlacement === 'large-left' ? '📍 Large Logo Left' :
                           '📍 Background Watermark'}
                </div>

                {settings.logoPlacement === 'left' && (
                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                    <div style={{ backgroundColor: '#ddd', padding: '4px 8px', borderRadius: '3px' }}>[LOGO]</div>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>Company Name</div>
                      <div>123 Business St</div>
                      <div>Phone: (*************</div>
                    </div>
                  </div>
                )}

                {settings.logoPlacement === 'right' && (
                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>Company Name</div>
                      <div>123 Business St</div>
                      <div>Phone: (*************</div>
                    </div>
                    <div style={{ backgroundColor: '#ddd', padding: '4px 8px', borderRadius: '3px' }}>[LOGO]</div>
                  </div>
                )}

                {settings.logoPlacement === 'above-center' && (
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ backgroundColor: '#ddd', padding: '4px 8px', borderRadius: '3px', display: 'inline-block', marginBottom: '4px' }}>[LOGO]</div>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>Company Name</div>
                      <div>123 Business St</div>
                      <div>Phone: (*************</div>
                    </div>
                  </div>
                )}

                {settings.logoPlacement === 'above-left' && (
                  <div>
                    <div style={{ backgroundColor: '#ddd', padding: '4px 8px', borderRadius: '3px', display: 'inline-block', marginBottom: '4px' }}>[LOGO]</div>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>Company Name</div>
                      <div>123 Business St</div>
                      <div>Phone: (*************</div>
                    </div>
                  </div>
                )}

                {settings.logoPlacement === 'large-left' && (
                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                    <div style={{ backgroundColor: '#ddd', padding: '8px 12px', borderRadius: '3px', fontSize: '10px' }}>[LARGE LOGO]</div>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>Company Name</div>
                      <div>123 Business St</div>
                      <div>Phone: (*************</div>
                    </div>
                  </div>
                )}

                {settings.logoPlacement === 'background' && (
                  <div style={{ position: 'relative' }}>
                    <div style={{
                      position: 'absolute',
                      right: '0',
                      top: '0',
                      backgroundColor: '#f0f0f0',
                      padding: '4px 8px',
                      borderRadius: '3px',
                      opacity: '0.3',
                      fontSize: '10px'
                    }}>[LOGO]</div>
                    <div style={{ position: 'relative', zIndex: '1' }}>
                      <div style={{ fontWeight: 'bold' }}>Company Name</div>
                      <div>123 Business St</div>
                      <div>Phone: (*************</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Primary Color
                </label>
                <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                  <input
                    type="color"
                    value={settings.primaryColor}
                    onChange={(e) => updateSetting('primaryColor', e.target.value)}
                    style={{ width: '40px', height: '32px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
                  />
                  <input
                    type="text"
                    value={settings.primaryColor}
                    onChange={(e) => updateSetting('primaryColor', e.target.value)}
                    style={{ flex: 1, padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                  />
                </div>
              </div>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Accent Color
                </label>
                <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                  <input
                    type="color"
                    value={settings.accentColor}
                    onChange={(e) => updateSetting('accentColor', e.target.value)}
                    style={{ width: '40px', height: '32px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
                  />
                  <input
                    type="text"
                    value={settings.accentColor}
                    onChange={(e) => updateSetting('accentColor', e.target.value)}
                    style={{ flex: 1, padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                  />
                </div>
              </div>
            </div>
          </div>
        );

      case 'typography':
        // Typography preset functions
        const applyTypographyPreset = (preset) => {
          console.log('Applying typography preset:', preset);

          const presets = {
            professional: {
              fontFamily: 'Arial',
              fontSize: 'medium',
              fontWeight: 'normal'
            },
            traditional: {
              fontFamily: 'Times New Roman',
              fontSize: 'medium',
              fontWeight: 'normal'
            },
            modern: {
              fontFamily: 'Trebuchet MS',
              fontSize: 'large',
              fontWeight: 'medium'
            },
            formal: {
              fontFamily: 'Garamond',
              fontSize: 'large',
              fontWeight: 'medium'
            }
          };

          const selectedPreset = presets[preset];
          console.log('Selected preset settings:', selectedPreset);

          if (selectedPreset) {
            updateSettings(selectedPreset);
            console.log('Settings updated with preset');
          } else {
            console.error('Preset not found:', preset);
          }
        };

        // Helper function to check if a preset is currently active
        const isPresetActive = (preset) => {
          const presets = {
            professional: { fontFamily: 'Arial', fontSize: 'medium', fontWeight: 'normal' },
            traditional: { fontFamily: 'Times New Roman', fontSize: 'medium', fontWeight: 'normal' },
            modern: { fontFamily: 'Trebuchet MS', fontSize: 'large', fontWeight: 'medium' },
            formal: { fontFamily: 'Garamond', fontSize: 'large', fontWeight: 'medium' }
          };

          const presetSettings = presets[preset];
          return presetSettings &&
                 settings.fontFamily === presetSettings.fontFamily &&
                 settings.fontSize === presetSettings.fontSize &&
                 settings.fontWeight === presetSettings.fontWeight;
        };

        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {/* Typography Presets */}
            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                Quick Presets
              </label>
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginBottom: '16px' }}>
                <button
                  type="button"
                  onClick={() => applyTypographyPreset('professional')}
                  style={{
                    padding: '12px 16px',
                    backgroundColor: isPresetActive('professional') ? '#1d4ed8' : '#3b82f6',
                    color: 'white',
                    border: isPresetActive('professional') ? '2px solid #fbbf24' : 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    textAlign: 'center',
                    transition: 'all 0.2s',
                    transform: isPresetActive('professional') ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onMouseEnter={(e) => {
                    if (!isPresetActive('professional')) {
                      e.target.style.backgroundColor = '#2563eb';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isPresetActive('professional')) {
                      e.target.style.backgroundColor = '#3b82f6';
                    }
                  }}
                >
                  💼 Professional
                  <div style={{ fontSize: '11px', opacity: '0.9', marginTop: '2px' }}>
                    Arial • Medium • Normal
                  </div>
                </button>

                <button
                  type="button"
                  onClick={() => applyTypographyPreset('traditional')}
                  style={{
                    padding: '12px 16px',
                    backgroundColor: isPresetActive('traditional') ? '#047857' : '#059669',
                    color: 'white',
                    border: isPresetActive('traditional') ? '2px solid #fbbf24' : 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    textAlign: 'center',
                    transition: 'all 0.2s',
                    transform: isPresetActive('traditional') ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onMouseEnter={(e) => {
                    if (!isPresetActive('traditional')) {
                      e.target.style.backgroundColor = '#047857';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isPresetActive('traditional')) {
                      e.target.style.backgroundColor = '#059669';
                    }
                  }}
                >
                  🏛️ Traditional
                  <div style={{ fontSize: '11px', opacity: '0.9', marginTop: '2px' }}>
                    Times New Roman • Medium • Normal
                  </div>
                </button>

                <button
                  type="button"
                  onClick={() => applyTypographyPreset('modern')}
                  style={{
                    padding: '12px 16px',
                    backgroundColor: isPresetActive('modern') ? '#6d28d9' : '#7c3aed',
                    color: 'white',
                    border: isPresetActive('modern') ? '2px solid #fbbf24' : 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    textAlign: 'center',
                    transition: 'all 0.2s',
                    transform: isPresetActive('modern') ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onMouseEnter={(e) => {
                    if (!isPresetActive('modern')) {
                      e.target.style.backgroundColor = '#6d28d9';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isPresetActive('modern')) {
                      e.target.style.backgroundColor = '#7c3aed';
                    }
                  }}
                >
                  🚀 Modern
                  <div style={{ fontSize: '11px', opacity: '0.9', marginTop: '2px' }}>
                    Trebuchet MS • Large • Medium
                  </div>
                </button>

                <button
                  type="button"
                  onClick={() => applyTypographyPreset('formal')}
                  style={{
                    padding: '12px 16px',
                    backgroundColor: isPresetActive('formal') ? '#b91c1c' : '#dc2626',
                    color: 'white',
                    border: isPresetActive('formal') ? '2px solid #fbbf24' : 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    fontWeight: '500',
                    cursor: 'pointer',
                    textAlign: 'center',
                    transition: 'all 0.2s',
                    transform: isPresetActive('formal') ? 'scale(1.02)' : 'scale(1)'
                  }}
                  onMouseEnter={(e) => {
                    if (!isPresetActive('formal')) {
                      e.target.style.backgroundColor = '#b91c1c';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isPresetActive('formal')) {
                      e.target.style.backgroundColor = '#dc2626';
                    }
                  }}
                >
                  👔 Formal
                  <div style={{ fontSize: '11px', opacity: '0.9', marginTop: '2px' }}>
                    Garamond • Large • Medium
                  </div>
                </button>
              </div>
              <p style={{ fontSize: '12px', color: '#6b7280', margin: '0' }}>
                Click any preset to instantly apply that typography style
              </p>
            </div>

            {/* Manual Font Controls */}
            <div style={{
              padding: '16px',
              backgroundColor: '#f9fafb',
              borderRadius: '6px',
              border: '1px solid #e5e7eb'
            }}>
              <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600' }}>
                🎛️ Manual Controls
              </h4>

              <div>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Font Family
                </label>
              <select
                value={settings.fontFamily}
                onChange={(e) => updateSetting('fontFamily', e.target.value)}
                style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
              >
                <option value="Arial">Arial (Sans-serif)</option>
                <option value="Helvetica">Helvetica (Sans-serif)</option>
                <option value="Times New Roman">Times New Roman (Serif)</option>
                <option value="Georgia">Georgia (Serif)</option>
                <option value="Courier New">Courier New (Monospace)</option>
                <option value="Verdana">Verdana (Sans-serif)</option>
                <option value="Tahoma">Tahoma (Sans-serif)</option>
                <option value="Trebuchet MS">Trebuchet MS (Sans-serif)</option>
                <option value="Palatino">Palatino (Serif)</option>
                <option value="Garamond">Garamond (Serif)</option>
              </select>
              <p style={{ fontSize: '12px', color: '#6b7280', margin: '4px 0 0 0' }}>
                Choose the font family for all invoice text
              </p>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Font Size
                </label>
                <select
                  value={settings.fontSize}
                  onChange={(e) => updateSetting('fontSize', e.target.value)}
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                >
                  <option value="small">Small (12px base)</option>
                  <option value="medium">Medium (14px base)</option>
                  <option value="large">Large (16px base)</option>
                  <option value="extra-large">Extra Large (18px base)</option>
                </select>
              </div>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Font Weight
                </label>
                <select
                  value={settings.fontWeight}
                  onChange={(e) => updateSetting('fontWeight', e.target.value)}
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                >
                  <option value="light">Light (300)</option>
                  <option value="normal">Normal (400)</option>
                  <option value="medium">Medium (500)</option>
                  <option value="bold">Bold (700)</option>
                </select>
              </div>
            </div>

            {/* Font Preview */}
            <div style={{
              padding: '16px',
              backgroundColor: '#f9fafb',
              borderRadius: '6px',
              border: '1px solid #e5e7eb',
              fontFamily: settings.fontFamily,
              fontSize: settings.fontSize === 'small' ? '12px' :
                        settings.fontSize === 'medium' ? '14px' :
                        settings.fontSize === 'large' ? '16px' : '18px',
              fontWeight: settings.fontWeight === 'light' ? '300' :
                         settings.fontWeight === 'normal' ? '400' :
                         settings.fontWeight === 'medium' ? '500' : '700'
            }}>
              <div style={{ fontWeight: 'bold', marginBottom: '8px', fontSize: '1.2em' }}>
                Font Preview
              </div>
              <div style={{ marginBottom: '8px' }}>
                <strong>Company Name</strong>
              </div>
              <div style={{ marginBottom: '4px' }}>
                123 Business Street, City, State 12345
              </div>
              <div style={{ marginBottom: '4px' }}>
                Phone: (************* | Email: <EMAIL>
              </div>
              <div style={{ marginTop: '12px', fontSize: '0.9em' }}>
                <strong>Invoice #INV-001</strong> | Date: {new Date().toLocaleDateString()}
              </div>
              <div style={{ marginTop: '8px', fontSize: '0.85em', color: '#6b7280' }}>
                This preview shows how your chosen font will appear on invoices.
              </div>
            </div>

            {/* Font Recommendations */}
            <div style={{
              padding: '12px',
              backgroundColor: '#eff6ff',
              borderRadius: '6px',
              border: '1px solid #bfdbfe'
            }}>
              <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: '600', color: '#1e40af' }}>
                💡 Font Recommendations
              </h4>
              <div style={{ fontSize: '12px', color: '#1e40af' }}>
                <div><strong>Professional:</strong> Arial, Helvetica, Verdana</div>
                <div><strong>Traditional:</strong> Times New Roman, Georgia</div>
                <div><strong>Modern:</strong> Trebuchet MS, Tahoma</div>
                <div><strong>Formal:</strong> Garamond, Palatino</div>
              </div>
            </div>

            {/* Font Colors Section */}
            <div style={{
              padding: '16px',
              backgroundColor: '#fef3c7',
              borderRadius: '6px',
              border: '1px solid #fbbf24'
            }}>
              <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600', color: '#92400e' }}>
                🎨 Font Colors
              </h4>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                {/* Primary Text Color */}
                <div>
                  <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px', color: '#92400e' }}>
                    Primary Text (Headings)
                  </label>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <input
                      type="color"
                      value={settings.primaryTextColor}
                      onChange={(e) => updateSetting('primaryTextColor', e.target.value)}
                      style={{ width: '40px', height: '32px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
                    />
                    <input
                      type="text"
                      value={settings.primaryTextColor}
                      onChange={(e) => updateSetting('primaryTextColor', e.target.value)}
                      style={{ flex: 1, padding: '6px 8px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '12px' }}
                    />
                  </div>
                </div>

                {/* Secondary Text Color */}
                <div>
                  <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px', color: '#92400e' }}>
                    Body Text
                  </label>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <input
                      type="color"
                      value={settings.secondaryTextColor}
                      onChange={(e) => updateSetting('secondaryTextColor', e.target.value)}
                      style={{ width: '40px', height: '32px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
                    />
                    <input
                      type="text"
                      value={settings.secondaryTextColor}
                      onChange={(e) => updateSetting('secondaryTextColor', e.target.value)}
                      style={{ flex: 1, padding: '6px 8px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '12px' }}
                    />
                  </div>
                </div>

                {/* Accent Text Color */}
                <div>
                  <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px', color: '#92400e' }}>
                    Accent (Totals, Highlights)
                  </label>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <input
                      type="color"
                      value={settings.accentTextColor}
                      onChange={(e) => updateSetting('accentTextColor', e.target.value)}
                      style={{ width: '40px', height: '32px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
                    />
                    <input
                      type="text"
                      value={settings.accentTextColor}
                      onChange={(e) => updateSetting('accentTextColor', e.target.value)}
                      style={{ flex: 1, padding: '6px 8px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '12px' }}
                    />
                  </div>
                </div>

                {/* Muted Text Color */}
                <div>
                  <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px', color: '#92400e' }}>
                    Muted (Notes, Descriptions)
                  </label>
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <input
                      type="color"
                      value={settings.mutedTextColor}
                      onChange={(e) => updateSetting('mutedTextColor', e.target.value)}
                      style={{ width: '40px', height: '32px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
                    />
                    <input
                      type="text"
                      value={settings.mutedTextColor}
                      onChange={(e) => updateSetting('mutedTextColor', e.target.value)}
                      style={{ flex: 1, padding: '6px 8px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '12px' }}
                    />
                  </div>
                </div>
              </div>

              {/* Color Presets */}
              <div style={{ marginTop: '16px' }}>
                <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '8px', color: '#92400e' }}>
                  Color Presets:
                </label>
                <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                  <button
                    type="button"
                    onClick={() => {
                      updateSetting('primaryTextColor', '#1f2937');
                      updateSetting('secondaryTextColor', '#374151');
                      updateSetting('accentTextColor', '#059669');
                      updateSetting('mutedTextColor', '#6b7280');
                    }}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#1f2937',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '11px',
                      cursor: 'pointer'
                    }}
                  >
                    Default
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      updateSetting('primaryTextColor', '#1e40af');
                      updateSetting('secondaryTextColor', '#3730a3');
                      updateSetting('accentTextColor', '#2563eb');
                      updateSetting('mutedTextColor', '#64748b');
                    }}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#1e40af',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '11px',
                      cursor: 'pointer'
                    }}
                  >
                    Blue
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      updateSetting('primaryTextColor', '#7c2d12');
                      updateSetting('secondaryTextColor', '#92400e');
                      updateSetting('accentTextColor', '#ea580c');
                      updateSetting('mutedTextColor', '#78716c');
                    }}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#7c2d12',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '11px',
                      cursor: 'pointer'
                    }}
                  >
                    Brown
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      updateSetting('primaryTextColor', '#000000');
                      updateSetting('secondaryTextColor', '#1f2937');
                      updateSetting('accentTextColor', '#374151');
                      updateSetting('mutedTextColor', '#6b7280');
                    }}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#000000',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '11px',
                      cursor: 'pointer'
                    }}
                  >
                    Black & Gray
                  </button>
                </div>
              </div>
            </div>
            </div>
          </div>
        );

      case 'invoice':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {/* Invoice Number Format Selection */}
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>
                Invoice Number Format
              </label>
              <div style={{ display: 'flex', gap: '16px' }}>
                <label style={{ display: 'flex', alignItems: 'center', gap: '6px', cursor: 'pointer' }}>
                  <input
                    type="radio"
                    name="invoiceNumberFormat"
                    value="prefix"
                    checked={settings.invoiceNumberFormat === 'prefix'}
                    onChange={(e) => updateSetting('invoiceNumberFormat', e.target.value)}
                  />
                  <span style={{ fontSize: '14px' }}>Prefix + Number (e.g., INV-001)</span>
                </label>
                <label style={{ display: 'flex', alignItems: 'center', gap: '6px', cursor: 'pointer' }}>
                  <input
                    type="radio"
                    name="invoiceNumberFormat"
                    value="number"
                    checked={settings.invoiceNumberFormat === 'number'}
                    onChange={(e) => updateSetting('invoiceNumberFormat', e.target.value)}
                  />
                  <span style={{ fontSize: '14px' }}>Simple Number (e.g., 1001)</span>
                </label>
              </div>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              {settings.invoiceNumberFormat === 'prefix' && (
                <div style={{ flex: 1 }}>
                  <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                    Invoice Prefix
                  </label>
                  <input
                    type="text"
                    value={settings.invoicePrefix}
                    onChange={(e) => updateSetting('invoicePrefix', e.target.value)}
                    placeholder="INV"
                    style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                  />
                </div>
              )}
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  {settings.invoiceNumberFormat === 'prefix' ? 'Starting Number' : 'Next Invoice Number'}
                </label>
                <input
                  type="number"
                  value={settings.invoiceStartingNumber}
                  onChange={(e) => updateSetting('invoiceStartingNumber', parseInt(e.target.value) || 1)}
                  placeholder="1"
                  min="1"
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                />
                <div style={{ fontSize: '12px', color: '#6b7280', marginTop: '4px' }}>
                  {settings.invoiceNumberFormat === 'prefix'
                    ? `Next invoice will be: ${settings.invoicePrefix}${String(settings.invoiceStartingNumber).padStart(3, '0')}`
                    : `Next invoice will be: ${settings.invoiceStartingNumber}`
                  }
                </div>
              </div>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Default Tax Rate (%)
                </label>
                <input
                  type="number"
                  value={settings.defaultTaxRate}
                  onChange={(e) => updateSetting('defaultTaxRate', parseFloat(e.target.value) || 0)}
                  step="0.1"
                  min="0"
                  max="100"
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                />
              </div>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Default Discount Rate (%)
                </label>
                <input
                  type="number"
                  value={settings.defaultDiscountRate}
                  onChange={(e) => updateSetting('defaultDiscountRate', parseFloat(e.target.value) || 0)}
                  step="0.1"
                  min="0"
                  max="100"
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                />
              </div>
            </div>

            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                Invoice Footer
              </label>
              <textarea
                value={settings.invoiceFooter}
                onChange={(e) => updateSetting('invoiceFooter', e.target.value)}
                rows={2}
                placeholder="Thank you for your business!"
                style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px', resize: 'vertical' }}
              />
            </div>

            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                Payment Instructions
              </label>
              <textarea
                value={settings.paymentInstructions}
                onChange={(e) => updateSetting('paymentInstructions', e.target.value)}
                rows={2}
                placeholder="Please remit payment within 30 days."
                style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px', resize: 'vertical' }}
              />
            </div>

            {/* Quantity Calculation Settings */}
            <div style={{
              padding: '16px',
              backgroundColor: '#f0fdf4',
              borderRadius: '6px',
              border: '1px solid #bbf7d0'
            }}>
              <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600', color: '#16a34a' }}>
                📐 Quantity Calculation Mode
              </h4>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                  <input
                    type="radio"
                    name="quantityMode"
                    value="simple"
                    checked={settings.quantityCalculationMode === 'simple'}
                    onChange={(e) => updateSetting('quantityCalculationMode', e.target.value)}
                    style={{ width: '16px', height: '16px' }}
                  />
                  <span style={{ fontSize: '14px', fontWeight: '500' }}>Simple Quantity</span>
                </label>
                <p style={{ fontSize: '12px', color: '#16a34a', margin: '0 0 8px 24px' }}>
                  Standard quantity input (1, 2, 3, etc.) - good for items, hours, units
                </p>

                <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                  <input
                    type="radio"
                    name="quantityMode"
                    value="area"
                    checked={settings.quantityCalculationMode === 'area'}
                    onChange={(e) => updateSetting('quantityCalculationMode', e.target.value)}
                    style={{ width: '16px', height: '16px' }}
                  />
                  <span style={{ fontSize: '14px', fontWeight: '500' }}>Area Calculation (Length × Width)</span>
                </label>
                <p style={{ fontSize: '12px', color: '#16a34a', margin: '0 0 0 24px' }}>
                  Calculate quantity from length and width - perfect for flooring, painting, carpeting
                </p>
              </div>
            </div>

            {/* Product Notes Settings */}
            <div style={{
              padding: '16px',
              backgroundColor: '#f0f9ff',
              borderRadius: '6px',
              border: '1px solid #bae6fd'
            }}>
              <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600', color: '#0369a1' }}>
                📝 Product Notes Settings
              </h4>

              <div>
                <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                  <input
                    type="checkbox"
                    checked={settings.showProductNotes}
                    onChange={(e) => updateSetting('showProductNotes', e.target.checked)}
                    style={{ width: '16px', height: '16px' }}
                  />
                  <span style={{ fontSize: '14px', fontWeight: '500' }}>Show Product Notes Fields</span>
                </label>
                <p style={{ fontSize: '12px', color: '#0369a1', margin: '6px 0 0 24px' }}>
                  Enable custom notes fields for each product line item. When disabled, only catalog descriptions will be shown.
                </p>
              </div>
            </div>
          </div>
        );

      case 'payment':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'flex', gap: '12px' }}>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Default Payment Terms
                </label>
                <select
                  value={settings.defaultPaymentTerms}
                  onChange={(e) => updateSetting('defaultPaymentTerms', e.target.value)}
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                >
                  <option value="Due on Receipt">Due on Receipt</option>
                  <option value="Net 15">Net 15</option>
                  <option value="Net 30">Net 30</option>
                  <option value="Net 45">Net 45</option>
                  <option value="Net 60">Net 60</option>
                </select>
              </div>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Default Due Days
                </label>
                <input
                  type="number"
                  value={settings.defaultDueDays}
                  onChange={(e) => updateSetting('defaultDueDays', parseInt(e.target.value) || 30)}
                  min="0"
                  max="365"
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                />
              </div>
            </div>

            <div style={{ display: 'flex', gap: '12px' }}>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Currency
                </label>
                <select
                  value={settings.currency}
                  onChange={(e) => updateSetting('currency', e.target.value)}
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                >
                  <option value="USD">USD - US Dollar</option>
                  <option value="EUR">EUR - Euro</option>
                  <option value="GBP">GBP - British Pound</option>
                  <option value="CAD">CAD - Canadian Dollar</option>
                  <option value="AUD">AUD - Australian Dollar</option>
                </select>
              </div>
              <div style={{ flex: 1 }}>
                <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                  Currency Symbol
                </label>
                <input
                  type="text"
                  value={settings.currencySymbol}
                  onChange={(e) => updateSetting('currencySymbol', e.target.value)}
                  maxLength="3"
                  style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
                />
              </div>
            </div>
          </div>
        );

      case 'paymentoptions':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {/* Pay Now Button Settings */}
            <div style={{
              padding: '16px',
              backgroundColor: '#f0fdf4',
              borderRadius: '6px',
              border: '1px solid #bbf7d0'
            }}>
              <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600', color: '#16a34a' }}>
                💳 Pay Now Button
              </h4>

              <div style={{ marginBottom: '12px' }}>
                <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                  <input
                    type="checkbox"
                    checked={settings.showPayNowButton}
                    onChange={(e) => updateSetting('showPayNowButton', e.target.checked)}
                    style={{ width: '16px', height: '16px' }}
                  />
                  <span style={{ fontSize: '14px', fontWeight: '500' }}>Show Pay Now Button</span>
                </label>
              </div>

              {settings.showPayNowButton && (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  <div>
                    <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px' }}>
                      Button Text
                    </label>
                    <input
                      type="text"
                      value={settings.payNowButtonText}
                      onChange={(e) => updateSetting('payNowButtonText', e.target.value)}
                      placeholder="Pay Now"
                      style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '14px' }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px' }}>
                      Payment URL
                    </label>
                    <input
                      type="url"
                      value={settings.payNowButtonUrl}
                      onChange={(e) => updateSetting('payNowButtonUrl', e.target.value)}
                      placeholder="https://your-payment-link.com"
                      style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '14px' }}
                    />
                    <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 0' }}>
                      Link to your payment processor (PayPal, Stripe, Square, etc.)
                    </p>
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px' }}>
                      Button Color
                    </label>
                    <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                      <input
                        type="color"
                        value={settings.payNowButtonColor}
                        onChange={(e) => updateSetting('payNowButtonColor', e.target.value)}
                        style={{ width: '40px', height: '32px', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
                      />
                      <input
                        type="text"
                        value={settings.payNowButtonColor}
                        onChange={(e) => updateSetting('payNowButtonColor', e.target.value)}
                        style={{ flex: 1, padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '14px' }}
                      />
                    </div>
                  </div>

                  {/* Button Preview */}
                  <div style={{ marginTop: '8px' }}>
                    <p style={{ fontSize: '12px', fontWeight: '500', marginBottom: '6px' }}>Preview:</p>
                    <button
                      style={{
                        padding: '12px 24px',
                        backgroundColor: settings.payNowButtonColor,
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        fontSize: '16px',
                        fontWeight: '600',
                        cursor: 'pointer',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                      }}
                    >
                      {settings.payNowButtonText}
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* QR Code Settings */}
            <div style={{
              padding: '16px',
              backgroundColor: '#eff6ff',
              borderRadius: '6px',
              border: '1px solid #bfdbfe'
            }}>
              <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600', color: '#2563eb' }}>
                📱 QR Code Payment
              </h4>

              <div style={{ marginBottom: '12px' }}>
                <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                  <input
                    type="checkbox"
                    checked={settings.showQRCode}
                    onChange={(e) => updateSetting('showQRCode', e.target.checked)}
                    style={{ width: '16px', height: '16px' }}
                  />
                  <span style={{ fontSize: '14px', fontWeight: '500' }}>Show QR Code</span>
                </label>
              </div>

              {settings.showQRCode && (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  <div>
                    <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px' }}>
                      QR Code Data (URL or Text)
                    </label>
                    <input
                      type="text"
                      value={settings.qrCodeData}
                      onChange={(e) => updateSetting('qrCodeData', e.target.value)}
                      placeholder="https://your-payment-link.com"
                      style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '14px' }}
                    />
                    <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 0' }}>
                      Payment URL, Venmo username, or any payment info
                    </p>
                  </div>

                  <div>
                    <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px' }}>
                      QR Code Size
                    </label>
                    <select
                      value={settings.qrCodeSize}
                      onChange={(e) => updateSetting('qrCodeSize', parseInt(e.target.value))}
                      style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '14px' }}
                    >
                      <option value={80}>Small (80px)</option>
                      <option value={120}>Medium (120px)</option>
                      <option value={160}>Large (160px)</option>
                      <option value={200}>Extra Large (200px)</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Payment Section Layout */}
            <div style={{
              padding: '16px',
              backgroundColor: '#f3f4f6',
              borderRadius: '6px',
              border: '1px solid #d1d5db'
            }}>
              <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600', color: '#374151' }}>
                📐 Payment Section Layout
              </h4>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <div>
                  <label style={{ display: 'block', fontSize: '12px', fontWeight: '500', marginBottom: '4px' }}>
                    Section Size
                  </label>
                  <select
                    value={settings.paymentSectionSize}
                    onChange={(e) => updateSetting('paymentSectionSize', e.target.value)}
                    style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '4px', fontSize: '14px' }}
                  >
                    <option value="small">Small - Compact layout</option>
                    <option value="medium">Medium - Standard layout</option>
                    <option value="large">Large - Prominent layout</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                    <input
                      type="checkbox"
                      checked={settings.showAmountInPayment}
                      onChange={(e) => updateSetting('showAmountInPayment', e.target.checked)}
                      style={{ width: '16px', height: '16px' }}
                    />
                    <span style={{ fontSize: '14px', fontWeight: '500' }}>Show Amount to Pay</span>
                  </label>
                  <p style={{ fontSize: '11px', color: '#6b7280', margin: '4px 0 0 24px' }}>
                    Display the invoice total in the payment section
                  </p>
                </div>
              </div>
            </div>

            {/* Payment Instructions */}
            <div style={{
              padding: '12px',
              backgroundColor: '#fef3c7',
              borderRadius: '6px',
              border: '1px solid #fbbf24'
            }}>
              <h4 style={{ margin: '0 0 8px 0', fontSize: '14px', fontWeight: '600', color: '#92400e' }}>
                💡 Payment Setup Tips
              </h4>
              <div style={{ fontSize: '12px', color: '#92400e' }}>
                <div><strong>Pay Now Button:</strong> Link to PayPal, Stripe, Square, or your payment processor</div>
                <div><strong>QR Code:</strong> Can contain payment URLs, Venmo usernames, or payment instructions</div>
                <div><strong>Both options:</strong> Will appear centered below the invoice total</div>
              </div>
            </div>
          </div>
        );

      case 'integration':
        return (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div>
              <label style={{ display: 'block', fontSize: '14px', fontWeight: '500', marginBottom: '6px' }}>
                Data Storage URL
              </label>
              <input
                type="url"
                value={settings.googleSheetsUrl}
                onChange={(e) => updateSetting('googleSheetsUrl', e.target.value)}
                placeholder="https://your-data-storage-url.com/..."
                style={{ width: '100%', padding: '8px 12px', border: '1px solid #d1d5db', borderRadius: '6px', fontSize: '14px' }}
              />
              <p style={{ fontSize: '12px', color: '#6b7280', margin: '4px 0 0 0' }}>
                Your data storage web app URL for data persistence
              </p>
            </div>

            <div style={{ padding: '16px', backgroundColor: '#f9fafb', borderRadius: '6px', border: '1px solid #e5e7eb' }}>
              <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600' }}>Settings Backup</h4>
              
              <div style={{ display: 'flex', gap: '8px', marginBottom: '12px' }}>
                <button
                  onClick={exportSettings}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    fontSize: '14px',
                    cursor: 'pointer'
                  }}
                >
                  📥 Export Settings
                </button>
                
                <label style={{
                  padding: '8px 16px',
                  backgroundColor: '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  cursor: 'pointer',
                  display: 'inline-block'
                }}>
                  📤 Import Settings
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleFileImport}
                    style={{ display: 'none' }}
                  />
                </label>
              </div>

              {importMessage && (
                <p style={{ 
                  fontSize: '12px', 
                  margin: '8px 0 0 0',
                  color: importMessage.includes('✅') ? '#16a34a' : '#dc2626'
                }}>
                  {importMessage}
                </p>
              )}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* CSS for loading spinner animation */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>

      <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      zIndex: 1002,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        width: '100%',
        maxWidth: '800px',
        maxHeight: '90vh',
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
      }}>
        {/* Header */}
        <div style={{
          padding: '24px 24px 0 24px',
          borderBottom: '1px solid #e5e7eb'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <h2 style={{ margin: 0, fontSize: '20px', fontWeight: '600', color: '#1f2937' }}>
                ⚙️ Settings
              </h2>
              {isSettingsLoading && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  fontSize: '12px',
                  color: '#6b7280',
                  backgroundColor: '#f3f4f6',
                  padding: '4px 8px',
                  borderRadius: '4px'
                }}>
                  <div style={{
                    width: '12px',
                    height: '12px',
                    border: '2px solid #e5e7eb',
                    borderTop: '2px solid #3b82f6',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                  Loading...
                </div>
              )}
            </div>
            <button
              onClick={() => setIsSettingsOpen(false)}
              style={{
                padding: '8px',
                backgroundColor: 'transparent',
                border: 'none',
                fontSize: '20px',
                cursor: 'pointer',
                borderRadius: '6px',
                color: '#6b7280'
              }}
            >
              ✕
            </button>
          </div>

          {/* Error Message */}
          {settingsError && (
            <div style={{
              marginTop: '12px',
              padding: '12px',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '6px',
              color: '#991b1b',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <span>⚠️</span>
              {settingsError}
            </div>
          )}

          {/* Data Storage Status */}
          <div style={{
            marginTop: '12px',
            padding: '8px 12px',
            backgroundColor: '#f0fdf4',
            border: '1px solid #bbf7d0',
            borderRadius: '6px',
            fontSize: '12px',
            color: '#16a34a',
            display: 'flex',
            alignItems: 'center',
            gap: '6px'
          }}>
            <span>📊</span>
            Settings are automatically saved locally and synchronized across sessions
          </div>

          {/* Tabs */}
          <div style={{ display: 'flex', gap: '4px' }}>
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: activeTab === tab.id ? '#3b82f6' : 'transparent',
                  color: activeTab === tab.id ? 'white' : '#6b7280',
                  border: 'none',
                  borderRadius: '6px',
                  fontSize: '14px',
                  cursor: 'pointer',
                  transition: 'all 0.2s'
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div style={{
          padding: '24px',
          overflowY: 'auto',
          flex: 1
        }}>
          {renderTabContent()}
        </div>

        {/* Footer */}
        <div style={{
          padding: '16px 24px',
          borderTop: '1px solid #e5e7eb',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <button
            onClick={resetSettings}
            style={{
              padding: '8px 16px',
              backgroundColor: '#dc2626',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer'
            }}
          >
            🔄 Reset to Defaults
          </button>

          {/* Save message */}
          {saveMessage && (
            <div style={{
              marginBottom: '10px',
              padding: '8px 12px',
              borderRadius: '4px',
              fontSize: '14px',
              fontWeight: '500',
              backgroundColor: saveMessage.includes('✅') ? '#d1fae5' : '#fee2e2',
              color: saveMessage.includes('✅') ? '#065f46' : '#991b1b',
              border: `1px solid ${saveMessage.includes('✅') ? '#a7f3d0' : '#fecaca'}`
            }}>
              {saveMessage}
            </div>
          )}

          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={() => setIsSettingsOpen(false)}
              style={{
                padding: '8px 16px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '14px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleSaveSettings}
              disabled={isSaving}
              style={{
                padding: '8px 16px',
                backgroundColor: isSaving ? '#9ca3af' : '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                fontSize: '14px',
                cursor: isSaving ? 'not-allowed' : 'pointer',
                opacity: isSaving ? 0.7 : 1
              }}
            >
              {isSaving ? '⏳ Saving...' : '✅ Save Settings'}
            </button>
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

// PropTypes validation
SettingsModal.propTypes = {
  // No props required - uses context for state management
};

export default SettingsModal;
