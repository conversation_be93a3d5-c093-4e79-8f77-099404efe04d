// Simple application configuration for Invoice App

/**
 * Data source types
 */
export const DATA_SOURCES = {
  SHEETS: 'sheets',
  API: 'api'
};

/**
 * Simple application configuration with hardcoded defaults
 */
export const appConfig = {
  // Data Source Configuration - default to Google Sheets
  dataSource: DATA_SOURCES.SHEETS,
  
  // Google Sheets Configuration (uses existing utility API)
  googleSheets: {
    enabled: true,
  },
  
  // Laravel API Configuration
  api: {
    baseUrl: 'http://localhost:8001/api',
    timeout: 10000,
  },
  
  // Authentication Configuration - disabled by default
  auth: {
    enabled: false,
    tokenStorageKey: 'invoice_auth_token',
    userStorageKey: 'invoice_user_data',
  },
  
  // App Configuration
  app: {
    name: 'Invoice App',
    version: '1.0.0',
    environment: 'development',
  },
  
  // Feature Flags
  features: {
    offlineMode: false,
    debugMode: true,
    analytics: false,
  },
  
  // UI Configuration
  ui: {
    theme: 'light',
    language: 'en',
    itemsPerPage: 15,
  },
  
  // Contact Information
  contact: {
    supportEmail: '<EMAIL>',
    companyEmail: '<EMAIL>',
  },
};

/**
 * Check if using Google Sheets data source
 */
export const isUsingSheets = () => {
  return appConfig.dataSource === DATA_SOURCES.SHEETS;
};

/**
 * Check if using API data source
 */
export const isUsingAPI = () => {
  return appConfig.dataSource === DATA_SOURCES.API;
};

/**
 * Check if authentication is enabled
 */
export const isAuthEnabled = () => {
  return appConfig.auth.enabled;
};

/**
 * Get API configuration
 */
export const getAPIConfig = () => {
  return appConfig.api;
};

/**
 * Get Google Sheets configuration
 */
export const getGoogleSheetsConfig = () => {
  return {
    enabled: true,
    note: 'Uses existing utility API implementation - no additional configuration needed'
  };
};

/**
 * Simple validation - always valid for now
 */
export const validateConfig = () => {
  return {
    isValid: true,
    errors: [],
    warnings: []
  };
};

/**
 * Log configuration (for debugging)
 */
export const logConfiguration = () => {
  if (appConfig.features.debugMode) {
    // console.group('📋 App Configuration');
    // console.log('Data Source:', appConfig.dataSource);
    // console.log('Authentication:', appConfig.auth.enabled ? 'Enabled' : 'Disabled');
    // console.log('Environment:', appConfig.app.environment);
    // console.log('Debug Mode:', appConfig.features.debugMode);
    // console.groupEnd();
  }
};

// Log configuration on import in development
if (appConfig.app.environment === 'development') {
  logConfiguration();
}
