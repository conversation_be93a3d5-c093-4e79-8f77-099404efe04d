// Application configuration based on environment variables

/**
 * Data source types
 */
export const DATA_SOURCES = {
  SHEETS: 'sheets',
  API: 'api'
};

/**
 * Get environment variable with fallback
 */
const getEnvVar = (key, defaultValue = '') => {
  // For React apps, just return the default value for now
  // Environment variables will be handled differently
  return defaultValue;
};

/**
 * Parse boolean environment variable
 */
const getBooleanEnvVar = (key, defaultValue = false) => {
  const value = getEnvVar(key, defaultValue.toString());
  return value === 'true' || value === '1';
};

/**
 * Parse number environment variable
 */
const getNumberEnvVar = (key, defaultValue = 0) => {
  const value = getEnvVar(key, defaultValue.toString());
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
};

/**
 * Application configuration object
 */
export const appConfig = {
  // Data Source Configuration
  dataSource: getEnvVar('REACT_APP_DATA_SOURCE', DATA_SOURCES.SHEETS),
  
  // Google Sheets Configuration (uses existing utility API)
  googleSheets: {
    // No configuration needed - uses existing utility API implementation
    enabled: true,
  },
  
  // Laravel API Configuration
  api: {
    baseUrl: getEnvVar('REACT_APP_API_BASE_URL', 'http://localhost:8001/api'),
    timeout: getNumberEnvVar('REACT_APP_API_TIMEOUT', 10000),
  },
  
  // Authentication Configuration
  auth: {
    enabled: getBooleanEnvVar('REACT_APP_AUTH_ENABLED', true),
    tokenStorageKey: getEnvVar('REACT_APP_TOKEN_STORAGE_KEY', 'invoice_auth_token'),
    userStorageKey: getEnvVar('REACT_APP_USER_STORAGE_KEY', 'invoice_user_data'),
  },
  
  // App Configuration
  app: {
    name: getEnvVar('REACT_APP_APP_NAME', 'Invoice App'),
    version: getEnvVar('REACT_APP_VERSION', '1.0.0'),
    environment: getEnvVar('REACT_APP_ENVIRONMENT', 'development'),
  },
  
  // Feature Flags
  features: {
    offlineMode: getBooleanEnvVar('REACT_APP_ENABLE_OFFLINE_MODE', false),
    debugMode: getBooleanEnvVar('REACT_APP_ENABLE_DEBUG_MODE', false),
    analytics: getBooleanEnvVar('REACT_APP_ENABLE_ANALYTICS', false),
  },
  
  // UI Configuration
  ui: {
    defaultTheme: getEnvVar('REACT_APP_DEFAULT_THEME', 'light'),
    defaultLanguage: getEnvVar('REACT_APP_DEFAULT_LANGUAGE', 'en'),
    itemsPerPage: getNumberEnvVar('REACT_APP_ITEMS_PER_PAGE', 15),
  },
  
  // Email Configuration
  email: {
    support: getEnvVar('REACT_APP_SUPPORT_EMAIL', '<EMAIL>'),
    company: getEnvVar('REACT_APP_COMPANY_EMAIL', '<EMAIL>'),
  },
};

/**
 * Check if using Google Sheets data source
 */
export const isUsingSheets = () => {
  return appConfig.dataSource === DATA_SOURCES.SHEETS;
};

/**
 * Check if using Laravel API data source
 */
export const isUsingAPI = () => {
  return appConfig.dataSource === DATA_SOURCES.API;
};

/**
 * Check if authentication is enabled
 */
export const isAuthEnabled = () => {
  return appConfig.auth.enabled && isUsingAPI();
};

/**
 * Get API base URL
 */
export const getAPIBaseURL = () => {
  return appConfig.api.baseUrl;
};

/**
 * Get Google Sheets configuration
 */
export const getGoogleSheetsConfig = () => {
  return {
    enabled: true,
    note: 'Uses existing utility API implementation - no additional configuration needed'
  };
};

/**
 * Validate configuration
 */
export const validateConfig = () => {
  const errors = [];
  const warnings = [];

  // Validate data source
  if (!Object.values(DATA_SOURCES).includes(appConfig.dataSource)) {
    errors.push(`Invalid data source: ${appConfig.dataSource}. Must be 'sheets' or 'api'.`);
  }

  // Google Sheets validation - no API key required as it uses existing utility API
  if (isUsingSheets()) {
    // Google Sheets mode uses the existing utility API implementation
    // No additional configuration required - it's ready to use out of the box
    if (appConfig.features.debugMode) {
      // console.log('✅ Google Sheets mode: Using existing utility API implementation');
    }
  }

  // Validate API config if using API
  if (isUsingAPI()) {
    if (!appConfig.api.baseUrl) {
      errors.push('API base URL is required when using API data source.');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Log configuration (for debugging)
 */
export const logConfig = () => {
  if (appConfig.features.debugMode) {
    // console.group('🔧 App Configuration');
    // console.log('Data Source:', appConfig.dataSource);
    // console.log('Authentication:', isAuthEnabled() ? 'Enabled' : 'Disabled');
    // console.log('Environment:', appConfig.app.environment);
    // console.log('API Base URL:', appConfig.api.baseUrl);
    // console.log('Features:', appConfig.features);
    // console.groupEnd();

    const validation = validateConfig();
    if (!validation.isValid) {
      // console.group('❌ Configuration Errors');
      // validation.errors.forEach(error => console.error(error));
      // console.groupEnd();
    }
  }
};

// Log configuration on import (development only)
if (appConfig.app.environment === 'development') {
  logConfig();
}

export default appConfig;
