// Settings context for managing app configuration
import React, { createContext, useContext, useState, useEffect } from 'react';
import dataService from '../services/dataService';

// Default settings
const DEFAULT_SETTINGS = {
  // Company Information
  companyName: 'Acme Corporation',
  companyAddress: '123 Business Street\nAnytown, ST 12345\nUnited States',
  companyPhone: '(*************',
  companyEmail: '<EMAIL>',
  companyWebsite: 'www.acmecorp.com',
  
  // Logo and Branding
  logoUrl: '',
  logoInputMethod: 'url', // 'url' or 'file'
  logoWidth: 150,
  logoHeight: 60,
  logoPlacement: 'left', // 'left', 'right', 'above-center', 'above-left', 'large-left', 'background'
  
  // Invoice Settings
  defaultTaxRate: 8.5,
  defaultDiscountRate: 0,
  invoicePrefix: 'INV',
  invoiceNumberLength: 3,
  
  // Payment Terms
  defaultPaymentTerms: 'Net 30',
  defaultDueDays: 30,

  // Payment Options
  showPayNowButton: true,
  payNowButtonText: 'Pay Now',
  payNowButtonUrl: 'https://your-payment-link.com',
  payNowButtonColor: '#10b981',
  showQRCode: true,
  qrCodeData: 'https://your-payment-link.com',
  qrCodeSize: 120,
  paymentSectionSize: 'medium', // 'small', 'medium', 'large'
  showAmountInPayment: true,
  
  // Colors and Theme
  primaryColor: '#1f2937',
  accentColor: '#3b82f6',

  // Typography
  fontFamily: 'Arial', // Font family for the entire invoice
  fontSize: 'medium', // 'small', 'medium', 'large'
  fontWeight: 'normal', // 'light', 'normal', 'bold'

  // Font Colors
  primaryTextColor: '#1f2937', // Main text color (headings, labels)
  secondaryTextColor: '#374151', // Secondary text color (body text)
  accentTextColor: '#059669', // Accent color (totals, highlights)
  mutedTextColor: '#6b7280', // Muted text color (notes, descriptions)

  // Product Notes Settings
  showProductNotes: true, // Show/hide custom notes fields for products
  quantityCalculationMode: 'simple', // 'simple' or 'area' (length x width)

  // Footer Information
  invoiceFooter: 'Thank you for your business!',
  paymentInstructions: 'Please remit payment within 30 days.',
  
  // Google Sheets Configuration
  googleSheetsUrl: '',
  
  // Currency
  currency: 'USD',
  currencySymbol: '$'
};

const SettingsContext = createContext();

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};

export const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(DEFAULT_SETTINGS);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isSettingsLoading, setIsSettingsLoading] = useState(true);
  const [settingsError, setSettingsError] = useState(null);

  // Load settings from localStorage first, then Google Sheets on mount
  useEffect(() => {
    const loadSettings = async () => {
      setIsSettingsLoading(true);
      setSettingsError(null);

      try {
        // COMMENTED OUT: Sheet initialization disabled for now
        // await dataService.initializeSettings();

        // Load settings (localStorage first, then Google Sheets fallback)
        const savedSettings = await dataService.fetchSettings();
        if (savedSettings && Object.keys(savedSettings).length > 0) {
          setSettings({ ...DEFAULT_SETTINGS, ...savedSettings });
          // console.log('Settings loaded and applied successfully');
        } else {
          // console.log('No saved settings found, using default values');
          setSettings(DEFAULT_SETTINGS);
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
        setSettingsError('Failed to load settings. Using default values.');
        // Keep default settings on error
        setSettings(DEFAULT_SETTINGS);
      } finally {
        setIsSettingsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Save settings whenever they change
  useEffect(() => {
    const saveSettings = async () => {
      try {
        await dataService.saveSettings(settings);
      } catch (error) {
        console.warn('Failed to save settings:', error);
      }
    };

    // Only save if settings have been loaded (not default)
    if (settings !== DEFAULT_SETTINGS) {
      saveSettings();
    }
  }, [settings]);

  // Update a specific setting
  const updateSetting = async (key, value) => {
    const newSettings = {
      ...settings,
      [key]: value
    };

    // Update local state immediately for responsive UI
    // The useEffect will handle saving automatically
    setSettings(newSettings);
  };

  // Update multiple settings at once
  const updateSettings = async (newSettingsData) => {
    const newSettings = {
      ...settings,
      ...newSettingsData
    };

    // Update local state immediately for responsive UI
    // The useEffect will handle saving automatically
    setSettings(newSettings);
  };

  // Reset settings to defaults
  const resetSettings = async () => {
    try {
      // Save default settings to API
      await dataService.saveSettings(DEFAULT_SETTINGS);
      // console.log('Settings reset to defaults on API');

      // Update local state
      setSettings(DEFAULT_SETTINGS);

      // Remove from localStorage
      localStorage.removeItem('invoiceAppSettings');
    } catch (error) {
      console.error('Error resetting settings on API:', error);
      // Still reset locally
      setSettings(DEFAULT_SETTINGS);
      localStorage.removeItem('invoiceAppSettings');
    }
  };

  // Export settings as JSON
  const exportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'invoice-app-settings.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Import settings from JSON file
  const importSettings = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const imported = JSON.parse(e.target.result);
          setSettings({ ...DEFAULT_SETTINGS, ...imported });
          resolve(imported);
        } catch (error) {
          reject(new Error('Invalid settings file'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  };

  const value = {
    settings,
    updateSetting,
    updateSettings,
    resetSettings,
    exportSettings,
    importSettings,
    isSettingsOpen,
    setIsSettingsOpen,
    isSettingsLoading,
    settingsError,
    DEFAULT_SETTINGS
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};
