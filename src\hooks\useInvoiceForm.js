// Custom hook for managing invoice form state
import { useState, useEffect, useCallback, useMemo } from 'react';
import { getLastInvoiceNumber, saveInvoice, formatInvoiceForSave } from '../services/invoiceService';
import { generateNextInvoiceNumber } from '../utils/invoiceNumberGenerator';
import { useSettings } from '../contexts/SettingsContext';

export const useInvoiceForm = () => {
  const { settings } = useSettings();

  // Basic invoice information
  const [invoiceNumber, setInvoiceNumber] = useState('');
  const [issueDate, setIssueDate] = useState(new Date().toISOString().split('T')[0]);
  const [dueDate, setDueDate] = useState(() => {
    const date = new Date();
    date.setDate(date.getDate() + (settings?.defaultDueDays || 30));
    return date.toISOString().split('T')[0];
  });

  // Customer and job site information
  const [selectedContact, setSelectedContact] = useState(null);
  const [jobSiteAddress, setJobSiteAddress] = useState('');
  const [jobSitePhone, setJobSitePhone] = useState('');

  // Invoice items and calculations
  const [invoiceItems, setInvoiceItems] = useState([]);
  const [taxRate, setTaxRate] = useState(settings?.defaultTaxRate || 0);
  const [discountRate, setDiscountRate] = useState(settings?.defaultDiscountRate || 0);

  // Loading states
  const [isLoadingInvoiceNumber, setIsLoadingInvoiceNumber] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState('');



  // Generate invoice number manually from last invoice number in Google Sheets
  const generateInvoiceNumber = useCallback(async () => {
    setIsLoadingInvoiceNumber(true);
    try {
      const nextNumber = await generateNextInvoiceNumber(settings);
      setInvoiceNumber(nextNumber);
    } catch (error) {
      console.error('Error generating invoice number:', error);
      // Fallback to time-based number using settings format
      const today = new Date();
      const timestamp = today.getTime().toString().slice(-6); // Last 6 digits of timestamp
      const numberLength = settings.invoiceNumberLength || 3;

      if (settings.invoiceNumberFormat === 'prefix') {
        const paddedTimestamp = timestamp.padStart(numberLength, '0');
        setInvoiceNumber(`${settings.invoicePrefix || 'INV-'}${paddedTimestamp}`);
      } else {
        const paddedTimestamp = timestamp.padStart(numberLength, '0');
        setInvoiceNumber(paddedTimestamp);
      }
    } finally {
      setIsLoadingInvoiceNumber(false);
    }
  }, [settings]);

  // Generate invoice number from last invoice only after settings are loaded
  useEffect(() => {
    if (!invoiceNumber && settings && Object.keys(settings).length > 0) {
      generateInvoiceNumber();
    }
  }, [generateInvoiceNumber, invoiceNumber, settings]);

  // Calculate totals with memoization for performance
  const totals = useMemo(() => {
    const subtotal = invoiceItems.reduce((sum, item) => {
      const price = item.price || 0;
      return sum + (price * item.quantity);
    }, 0);

    const discountAmount = subtotal * (discountRate / 100);
    const afterDiscount = subtotal - discountAmount;
    const taxAmount = afterDiscount * (taxRate / 100);
    const total = afterDiscount + taxAmount;

    return {
      subtotal,
      discountAmount,
      taxAmount,
      total
    };
  }, [invoiceItems, discountRate, taxRate]);

  // Reset form
  const resetForm = () => {
    setSelectedContact(null);
    setInvoiceItems([]);
    setJobSiteAddress('');
    setJobSitePhone('');
    setTaxRate(0);
    setDiscountRate(0);
    // Keep invoice number, dates as they should auto-generate/default
  };



  // Save invoice function
  const handleSaveInvoice = useCallback(async () => {
    if (!selectedContact || invoiceItems.length === 0) {
      setSaveMessage('Please select a contact and add items before saving.');
      setTimeout(() => setSaveMessage(''), 3000);
      return;
    }

    setIsSaving(true);
    try {
      const invoiceData = formatInvoiceForSave({
        invoiceNumber,
        issueDate,
        dueDate,
        selectedContact,
        invoiceItems,
        taxRate,
        discountRate,
        jobSiteAddress,
        jobSitePhone
      });

      await saveInvoice(invoiceData);
      setSaveMessage('✅ Invoice saved successfully!');
      setTimeout(() => setSaveMessage(''), 3000);
    } catch (error) {
      console.error('Error saving invoice:', error);
      setSaveMessage('❌ Error saving invoice. Please try again.');
      setTimeout(() => setSaveMessage(''), 3000);
    } finally {
      setIsSaving(false);
    }
  }, [invoiceNumber, issueDate, dueDate, selectedContact, invoiceItems, taxRate, discountRate, jobSiteAddress, jobSitePhone]);

  // Clear invoice function
  const handleClearInvoice = useCallback(() => {
    setSelectedContact(null);
    setInvoiceItems([]);
    setTaxRate(0);
    setDiscountRate(0);
    setJobSiteAddress('');
    setJobSitePhone('');
    generateInvoiceNumber(); // Generate new invoice number
  }, [generateInvoiceNumber]);

  // Load invoice data into form
  const loadInvoiceData = (invoiceData) => {
    setInvoiceNumber(invoiceData.invoiceNumber);
    setIssueDate(invoiceData.issueDate);
    setDueDate(invoiceData.dueDate);
    setJobSiteAddress(invoiceData.jobSiteAddress || '');
    setJobSitePhone(invoiceData.jobSitePhone || '');
    setTaxRate(invoiceData.taxRate || 0);
    setDiscountRate(invoiceData.discountRate || 0);

    if (invoiceData.items && Array.isArray(invoiceData.items)) {
      setInvoiceItems(invoiceData.items);
    }
  };

  return {
    // State
    invoiceNumber,
    setInvoiceNumber,
    issueDate,
    setIssueDate,
    dueDate,
    setDueDate,
    selectedContact,
    setSelectedContact,
    jobSiteAddress,
    setJobSiteAddress,
    jobSitePhone,
    setJobSitePhone,
    invoiceItems,
    setInvoiceItems,
    taxRate,
    setTaxRate,
    discountRate,
    setDiscountRate,

    // UI State
    isLoadingInvoiceNumber,
    isSaving,
    saveMessage,

    // Computed values
    totals,

    // Actions
    generateInvoiceNumber,
    handleSaveInvoice,
    handleClearInvoice,
    resetForm,
    loadInvoiceData
  };
};
