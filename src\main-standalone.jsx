import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import './responsive.css'
import App from './App.jsx'

// Ensure responsive design works by adding viewport meta tag if not present
function ensureViewportMeta() {
  if (!document.querySelector('meta[name="viewport"]')) {
    const meta = document.createElement('meta');
    meta.name = 'viewport';
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
    document.head.appendChild(meta);
    console.log('Added viewport meta tag for responsive design');
  }
}

// Add responsive CSS if not already present
function ensureResponsiveStyles() {
  if (!document.querySelector('#invoice-app-responsive-styles')) {
    const style = document.createElement('style');
    style.id = 'invoice-app-responsive-styles';
    style.textContent = `
      /* Ensure the invoice app container is responsive */
      .invoice-app-container,
      #invoice-app,
      #invoice-app-root {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: auto !important;
        box-sizing: border-box !important;
      }

      /* Mobile responsive fixes */
      @media (max-width: 768px) {
        .invoice-app-container,
        #invoice-app,
        #invoice-app-root {
          padding: 8px !important;
          margin: 0 !important;
        }
      }
    `;
    document.head.appendChild(style);
    console.log('Added responsive styles for invoice app');
  }
}

// Standalone version of the Invoice App
// This file is used for building embeddable versions of the app

// Function to initialize the Invoice App in any container
window.InvoiceApp = {
  // Initialize the app in a specific container
  init: function(containerId = 'invoice-app-root') {
    // Ensure responsive design works
    ensureViewportMeta();
    ensureResponsiveStyles();

    const container = document.getElementById(containerId);
    if (!container) {
      console.error(`Container with id "${containerId}" not found`);
      return;
    }

    // Add responsive container styles
    container.style.width = '100%';
    container.style.minHeight = '100vh';
    container.style.overflow = 'auto';
    container.style.boxSizing = 'border-box';

    const root = createRoot(container);
    root.render(
      <StrictMode>
        <App />
      </StrictMode>
    );

    console.log('Invoice App initialized successfully with responsive design');
    return root;
  },

  // Create and initialize the app in a new container
  create: function(parentElement = document.body) {
    // Ensure responsive design works
    ensureViewportMeta();
    ensureResponsiveStyles();

    const container = document.createElement('div');
    container.id = 'invoice-app-root-' + Date.now();
    container.style.width = '100%';
    container.style.minHeight = '100vh';
    container.style.overflow = 'auto';
    container.style.boxSizing = 'border-box';
    parentElement.appendChild(container);

    return this.init(container.id);
  },

  // Version info
  version: '1.0.0'
};

// Auto-initialize if there's a container with the default ID
document.addEventListener('DOMContentLoaded', function() {
  // Try common container IDs
  const containerIds = ['invoice-app-root', 'invoice-app', 'invoiceapp'];

  for (const containerId of containerIds) {
    const container = document.getElementById(containerId);
    if (container) {
      window.InvoiceApp.init(containerId);
      break; // Only initialize the first found container
    }
  }
});

// Export for module systems
export default window.InvoiceApp;
