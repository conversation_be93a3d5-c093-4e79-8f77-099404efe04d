// Google Sheets data service for the Invoice Application
// Simplified to use only Google Sheets as the data source

/**
 * Google Sheets data service
 * All data operations use Google Sheets as the backend
 */
const dataService = {
  // Contact methods
  async fetchContacts() {
    const { fetchContacts } = await import('../utils/api');
    return await fetchContacts();
  },

  async searchContacts(query) {
    const { searchContacts } = await import('../utils/api');
    return await searchContacts(query);
  },

  async createContact(contactData) {
    try {
      const { saveContactData } = await import('../utils/api');
      await saveContactData(contactData);
      // console.log('Contact created successfully in Google Sheets');
      return contactData;
    } catch (error) {
      console.error('Failed to create contact in Google Sheets:', error);
      throw error;
    }
  },

  async updateContact(id, contactData) {
    console.warn('Updating contacts in Google Sheets not implemented yet');
    return contactData;
  },

  async deleteContact(id) {
    console.warn('Deleting contacts in Google Sheets not implemented yet');
    return true;
  },

  // Product methods
  async fetchProducts() {
    const { fetchProducts } = await import('../utils/api');
    return await fetchProducts();
  },

  async searchProducts(query) {
    const { searchProducts } = await import('../utils/api');
    return await searchProducts(query);
  },

  async createProduct(productData) {
    try {
      const { saveProductData } = await import('../utils/api');
      await saveProductData(productData);
      // console.log('Product created successfully in Google Sheets');
      return productData;
    } catch (error) {
      console.error('Failed to create product in Google Sheets:', error);
      throw error;
    }
  },

  async updateProduct(id, productData) {
    console.warn('Updating products in Google Sheets not implemented yet');
    return productData;
  },

  async deleteProduct(id) {
    console.warn('Deleting products in Google Sheets not implemented yet');
    return true;
  },

  // Invoice methods
  async fetchInvoices() {
    const { fetchInvoices } = await import('../utils/api');
    return await fetchInvoices();
  },

  async saveInvoice(invoiceData) {
    const { saveInvoiceData } = await import('../utils/api');
    return await saveInvoiceData(invoiceData);
  },

  async createInvoice(invoiceData) {
    return await this.saveInvoice(invoiceData);
  },

  async updateInvoice(id, invoiceData) {
    console.warn('Updating invoices in Google Sheets not implemented yet');
    return invoiceData;
  },

  async deleteInvoice(id) {
    console.warn('Deleting invoices in Google Sheets not implemented yet');
    return true;
  },

  // Settings methods
  async fetchSettings() {
    // First, check localStorage for existing settings
    const localSettings = localStorage.getItem('invoice_settings');

    if (localSettings && localSettings.trim() !== '' && localSettings !== 'null' && localSettings !== 'undefined') {
      try {
        const parsedLocalSettings = JSON.parse(localSettings);
        // Check if it's a valid object with at least one meaningful property
        if (parsedLocalSettings &&
            typeof parsedLocalSettings === 'object' &&
            !Array.isArray(parsedLocalSettings) &&
            Object.keys(parsedLocalSettings).length > 0) {
          // console.log('✅ Settings loaded from localStorage (fast load)');
          return parsedLocalSettings;
        }
      } catch (error) {
        console.warn('Failed to parse localStorage settings:', error);
        // Clear invalid localStorage data
        localStorage.removeItem('invoice_settings');
      }
    }

    // If no valid localStorage settings, try to fetch from Google Sheets
    try {
      console.log('📡 No localStorage settings found, fetching from Google Sheets...');
      const { fetchSettings: fetchSheetsSettings } = await import('../utils/api');
      const sheetsSettings = await fetchSheetsSettings();

      console.log('📋 Google Sheets response:', sheetsSettings);
      console.log('📋 Response type:', typeof sheetsSettings);
      console.log('📋 Response keys:', Object.keys(sheetsSettings || {}));

      if (sheetsSettings &&
          typeof sheetsSettings === 'object' &&
          !Array.isArray(sheetsSettings) &&
          Object.keys(sheetsSettings).length > 0) {
        console.log('✅ Settings loaded from Google Sheets and saved to localStorage for next time');
        // Save to localStorage for future fast access
        localStorage.setItem('invoice_settings', JSON.stringify(sheetsSettings));
        return sheetsSettings;
      } else {
        console.log('📭 No settings found in Google Sheets or empty response');
      }
    } catch (error) {
      console.warn('❌ Failed to fetch settings from Google Sheets:', error);
    }

    // Return empty object if no settings found anywhere
    // console.log('🔄 No settings found in localStorage or Google Sheets, using defaults');
    return {};
  },

  async saveSettings(settings) {
    // Save to localStorage immediately for fast access
    localStorage.setItem('invoice_settings', JSON.stringify(settings));

    try {
      // Also save to Google Sheets for persistence across devices
      const { saveSettingsData } = await import('../utils/api');
      await saveSettingsData(settings);
      // console.log('Settings saved to both localStorage and Google Sheets');
    } catch (error) {
      console.warn('Failed to save settings to Google Sheets, saved to localStorage only:', error);
      // Don't throw error - localStorage save was successful
    }

    return settings;
  },

  // Initialize settings sheet in Google Sheets
  async initializeSettings() {
    try {
      const { initializeSettingsSheet } = await import('../utils/api');
      return await initializeSettingsSheet();
    } catch (error) {
      console.warn('Failed to initialize settings sheet:', error);
      return false;
    }
  }
};

// Export the data service
export { dataService };
export default dataService;
