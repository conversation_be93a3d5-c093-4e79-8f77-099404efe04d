// API configuration and utilities
export const API_CONFIG = {
  
  BASE_URL: "https://script.google.com/macros/s/AKfycbzLlJKzpgDV38UFlQHCoOJq7X4NZG685Abd4l5tpKQq8Gls-CSd7ZR-YeAMNan0QyAahw/exec",
  ENDPOINTS: {
    CONTACTS: 'contacts',
    PRODUCTS: 'products',
    INVOICES: 'invoices',
    SETTINGS: 'settings'
  }
};

/**
 * Generic API fetch function with error handling
 * @param {string} type - The type of data to fetch (contacts, products)
 * @returns {Promise<any>} - The fetched data
 */
export const fetchData = async (type) => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}?type=${type}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch ${type}: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching ${type}:`, error);
    throw new Error(`Unable to load ${type}. Please check your connection and try again.`);
  }
};

/**
 * Fetch contacts from the API
 * @returns {Promise<Array>} - Array of contact objects
 */
export const fetchContacts = () => fetchData(API_CONFIG.ENDPOINTS.CONTACTS);

/**
 * Search contacts from the API with a search term
 * @param {string} searchTerm - The search term to filter contacts
 * @returns {Promise<Array>} - Array of filtered contact objects
 */
export const searchContacts = async (searchTerm) => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}?type=${API_CONFIG.ENDPOINTS.CONTACTS}&search=${encodeURIComponent(searchTerm)}`);

    if (!response.ok) {
      throw new Error(`Failed to search contacts: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error searching contacts:', error);
    throw new Error('Unable to search contacts. Please check your connection and try again.');
  }
};

/**
 * Fetch products from the API
 * @returns {Promise<Array>} - Array of product objects
 */
export const fetchProducts = () => fetchData(API_CONFIG.ENDPOINTS.PRODUCTS);

/**
 * Search products from the API with a search term
 * @param {string} searchTerm - The search term to filter products
 * @returns {Promise<Array>} - Array of filtered product objects
 */
export const searchProducts = async (searchTerm) => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}?type=${API_CONFIG.ENDPOINTS.PRODUCTS}&search=${encodeURIComponent(searchTerm)}`);

    if (!response.ok) {
      throw new Error(`Failed to search products: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error searching products:', error);
    throw new Error('Unable to search products. Please check your connection and try again.');
  }
};

/**
 * Get the last invoice number from the API
 * @returns {Promise<string>} - The last invoice number
 */
export const getLastInvoiceNumber = async () => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}?type=${API_CONFIG.ENDPOINTS.INVOICES}&action=getLastInvoiceNumber`);

    if (!response.ok) {
      throw new Error(`Failed to get last invoice number: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.lastInvoice || null;
  } catch (error) {
    console.error('Error getting last invoice number:', error);
    throw new Error('Unable to get last invoice number. Please check your connection and try again.');
  }
};

/**
 * Fetch invoices from the API
 * @returns {Promise<Array>} - Array of invoice objects
 */
export const fetchInvoices = () => fetchData(API_CONFIG.ENDPOINTS.INVOICES);

/**
 * Save invoice data to the API (supports multiple rows for multiple products)
 * @param {Array|Object} invoiceData - The invoice data to save (array of rows or single object)
 * @returns {Promise<Object>} - Response from the server
 */
export const saveInvoiceData = async (invoiceData) => {
  try {
    // Ensure invoiceData is an array for consistent handling
    const dataToSave = Array.isArray(invoiceData) ? invoiceData : [invoiceData];

    // console.log('Attempting to save invoice to:', API_CONFIG.BASE_URL);
    // console.log('Data being sent:', {
    //   type: API_CONFIG.ENDPOINTS.INVOICES,
    //   action: 'saveInvoice',
    //   data: dataToSave,
    //   multipleRows: true
    // });

    const response = await fetch(API_CONFIG.BASE_URL, {
      method: 'POST',
      mode: 'no-cors',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: API_CONFIG.ENDPOINTS.INVOICES,
        action: 'saveInvoice',
        data: dataToSave,
        multipleRows: true // Flag to indicate multiple rows
      })
    });

    // console.log('Response status:', response.status);
    // console.log('Response headers:', response.headers);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Response error text:', errorText);
      throw new Error(`Failed to save invoice: ${response.status} ${response.statusText}. Response: ${errorText}`);
    }

    const result = await response.json();
    // console.log('Save invoice result:', result);
    return result;
  } catch (error) {
    console.error('Detailed error saving invoice:', error);
    console.error('Error type:', error.constructor.name);
    console.error('Error message:', error.message);

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('Network error: Unable to connect to Google Sheets. Please check your internet connection and Google Apps Script URL.');
    } else if (error.message.includes('Failed to save invoice')) {
      throw error; // Re-throw with original detailed message
    } else {
      throw new Error(`Unable to save invoice: ${error.message}. Please check your connection and try again.`);
    }
  }
};

/**
 * Get the last invoice number from the API
 * @returns {Promise<string>} - The last invoice number
 */
export const fetchLastInvoiceNumber = async () => {
  try {
    const url = `${API_CONFIG.BASE_URL}?type=${API_CONFIG.ENDPOINTS.INVOICES}&action=getLastInvoiceNumber`;
    // console.log('Fetching last invoice number from:', url);

    const response = await fetch(url);

    // console.log('Last invoice number response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Last invoice number error response:', errorText);
      throw new Error(`Failed to get last invoice number: ${response.status} ${response.statusText}. Response: ${errorText}`);
    }

    const data = await response.json();
    // console.log('Last invoice number data:', data);
    return data.lastInvoice || null;
  } catch (error) {
    console.error('Error getting last invoice number:', error);

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      console.warn('Network error getting last invoice number, using fallback');
      return null; // Return null to trigger fallback invoice number generation
    } else {
      throw new Error(`Unable to get last invoice number: ${error.message}. Please check your connection and try again.`);
    }
  }
};

/**
 * Generate the next invoice number with a specific prefix
 * @param {string} prefix - The desired prefix (e.g., "INV-", "QUOTE-")
 * @param {string} lastInvoiceNumber - The last invoice number (optional)
 * @returns {string} - The next invoice number with the specified prefix
 */
export const generateNextInvoiceNumberWithPrefix = (prefix, lastInvoiceNumber = null) => {
  // console.log('Generating next invoice number with prefix:', prefix, 'from:', lastInvoiceNumber);

  // If no last invoice or it doesn't match the prefix, start with 001
  if (!lastInvoiceNumber || !lastInvoiceNumber.startsWith(prefix)) {
    const result = `${prefix}001`;
    // console.log('No matching prefix found, starting with:', result);
    return result;
  }

  // Use the existing logic to increment
  return generateNextInvoiceNumber(lastInvoiceNumber);
};

/**
 * Generate the next invoice number based on the last one
 * @param {string} lastInvoiceNumber - The last invoice number (e.g., "INV-001")
 * @returns {string} - The next invoice number (e.g., "INV-002")
 */
export const generateNextInvoiceNumber = (lastInvoiceNumber) => {
  // console.log('Generating next invoice number from:', lastInvoiceNumber);

  if (!lastInvoiceNumber || lastInvoiceNumber.trim() === '') {
    // If no previous invoice, start with INV-001
    // console.log('No previous invoice found, starting with INV-001');
    return 'INV-001';
  }

  // Clean the input and extract the number part from the invoice number
  const cleanInput = lastInvoiceNumber.toString().trim();
  const match = cleanInput.match(/^(.+?)(\d+)$/);

  if (match) {
    const prefix = match[1]; // e.g., "INV-"
    const numberStr = match[2]; // e.g., "001"
    const currentNumber = parseInt(numberStr, 10); // e.g., 1
    const nextNumber = currentNumber + 1; // Increment by 1

    // console.log(`Parsed: prefix="${prefix}", current=${currentNumber}, next=${nextNumber}`);

    // Pad with zeros to maintain the same length as the original
    const paddedNumber = nextNumber.toString().padStart(numberStr.length, '0');
    const result = `${prefix}${paddedNumber}`;

    // console.log('Generated next invoice number:', result);
    return result;
  } else {
    // If format doesn't match expected pattern, generate a new one
    // console.log('Invoice number format not recognized, generating new format');
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const fallback = `INV-${year}${month}001`;
    // console.log('Generated fallback invoice number:', fallback);
    return fallback;
  }
};

/**
 * Save contact data to the API
 * @param {Array|Object} contactData - The contact data to save (array of contacts or single object)
 * @returns {Promise<Object>} - Response from the server
 */
export const saveContactData = async (contactData) => {
  try {
    // Ensure contactData is an array for consistent handling
    const dataToSave = Array.isArray(contactData) ? contactData : [contactData];

    // console.log('Attempting to save contact to:', API_CONFIG.BASE_URL);
    // console.log('Data being sent:', {
    //   type: API_CONFIG.ENDPOINTS.CONTACTS,
    //   action: 'saveContact',
    //   data: dataToSave
    // });

    const response = await fetch(API_CONFIG.BASE_URL, {
      method: 'POST',
      mode: 'no-cors',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: API_CONFIG.ENDPOINTS.CONTACTS,
        action: 'saveContact',
        data: dataToSave
      })
    });

    // console.log('Contact save response received');
    return { success: true, message: 'Contact saved successfully' };
  } catch (error) {
    console.error('Error saving contact:', error);
    throw new Error(`Failed to save contact: ${error.message}`);
  }
};

/**
 * Save product data to the API
 * @param {Array|Object} productData - The product data to save (array of products or single object)
 * @returns {Promise<Object>} - Response from the server
 */
export const saveProductData = async (productData) => {
  try {
    // Ensure productData is an array for consistent handling
    const dataToSave = Array.isArray(productData) ? productData : [productData];

    // console.log('Attempting to save product to:', API_CONFIG.BASE_URL);
    // console.log('Data being sent:', {
    //   type: API_CONFIG.ENDPOINTS.PRODUCTS,
    //   action: 'saveProduct',
    //   data: dataToSave
    // });

    const response = await fetch(API_CONFIG.BASE_URL, {
      method: 'POST',
      mode: 'no-cors',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: API_CONFIG.ENDPOINTS.PRODUCTS,
        action: 'saveProduct',
        data: dataToSave
      })
    });

    // console.log('Product save response received');
    return { success: true, message: 'Product saved successfully' };
  } catch (error) {
    console.error('Error saving product:', error);
    throw new Error(`Failed to save product: ${error.message}`);
  }
};

/**
 * Fetch settings from Google Sheets
 * @returns {Promise<Object>} - The settings object
 */
export const fetchSettings = async () => {
  try {
    // console.log('Fetching settings from Google Sheets...');
    const response = await fetch(`${API_CONFIG.BASE_URL}?type=${API_CONFIG.ENDPOINTS.SETTINGS}&action=getSettings`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    // console.log('Settings fetched successfully:', data);

    // Handle different response formats from Google Sheets
    if (Array.isArray(data)) {
      if (data.length === 0) {
        return {}; // No settings found
      }

      // If it's an array of key-value objects (preferred format)
      if (data.length > 0 && typeof data[0] === 'object') {
        const settings = {};

        // Check if it's a single row with all settings as columns
        if (data.length === 1) {
          const settingsRow = data[0];
          Object.keys(settingsRow).forEach(key => {
            if (settingsRow[key] !== null && settingsRow[key] !== undefined && settingsRow[key] !== '') {
              // Try to parse JSON values, fallback to original value
              try {
                settings[key] = JSON.parse(settingsRow[key]);
              } catch {
                settings[key] = settingsRow[key];
              }
            }
          });
        } else {
          // Handle multiple rows where each row is a key-value pair
          data.forEach(row => {
            if (row.key && row.value !== undefined) {
              try {
                settings[row.key] = JSON.parse(row.value);
              } catch {
                settings[row.key] = row.value;
              }
            }
          });
        }

        return settings;
      }
    }

    // If data is already an object, return it
    if (typeof data === 'object' && data !== null) {
      return data;
    }

    // Fallback to empty object
    return {};
  } catch (error) {
    console.warn('Error fetching settings from Google Sheets:', error);
    return {}; // Return empty object on error
  }
};

/**
 * Save settings to Google Sheets
 * @param {Object} settings - The settings object to save
 * @returns {Promise<Object>} - The saved settings
 */
export const saveSettingsData = async (settings) => {
  try {
    // console.log('Attempting to save settings to:', API_CONFIG.BASE_URL);

    // Convert settings object to a format suitable for Google Sheets
    // Each setting becomes a key-value pair that will be saved as a row
    const settingsData = {};
    Object.keys(settings).forEach(key => {
      const value = settings[key];
      // Convert objects and arrays to JSON strings for storage
      if (typeof value === 'object' && value !== null) {
        settingsData[key] = JSON.stringify(value);
      } else {
        settingsData[key] = String(value); // Ensure all values are strings for Google Sheets
      }
    });

    // console.log('Data being sent:', {
    //   type: API_CONFIG.ENDPOINTS.SETTINGS,
    //   action: 'saveSettings',
    //   data: settingsData
    // });

    const response = await fetch(API_CONFIG.BASE_URL, {
      method: 'POST',
      mode: 'no-cors',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        type: API_CONFIG.ENDPOINTS.SETTINGS,
        action: 'saveSettings',
        data: settingsData,
        // Add metadata to help the Apps Script understand the format
        format: 'key-value-pairs',
        operation: 'upsert' // Update existing or insert new
      })
    });

    // console.log('Settings save response received');
    return { success: true, message: 'Settings saved successfully' };
  } catch (error) {
    console.error('Error saving settings to Google Sheets:', error);
    throw new Error(`Failed to save settings: ${error.message}`);
  }
};

// Keep the old function name for backward compatibility
export const saveSettings = saveSettingsData;

/**
 * Test settings save and load functionality
 * @returns {Promise<Object>} - Test results
 */
export const testSettingsIntegration = async () => {
  const testResults = {
    timestamp: new Date().toISOString(),
    tests: []
  };

  try {
    // Test 1: Save test settings
    const testSettings = {
      companyName: 'Test Company',
      companyEmail: '<EMAIL>',
      testObject: { nested: 'value', number: 42 },
      testArray: ['item1', 'item2'],
      testBoolean: true,
      testNumber: 123
    };

    testResults.tests.push({
      name: 'Save Test Settings',
      status: 'attempting',
      data: testSettings
    });

    await saveSettingsData(testSettings);
    testResults.tests[0].status = 'success';

    // Test 2: Fetch settings back
    testResults.tests.push({
      name: 'Fetch Settings',
      status: 'attempting'
    });

    const fetchedSettings = await fetchSettings();
    testResults.tests[1].status = 'success';
    testResults.tests[1].data = fetchedSettings;

    // Test 3: Compare data integrity
    testResults.tests.push({
      name: 'Data Integrity Check',
      status: 'checking',
      comparison: {
        saved: testSettings,
        fetched: fetchedSettings,
        matches: {}
      }
    });

    const matches = {};
    Object.keys(testSettings).forEach(key => {
      const savedValue = testSettings[key];
      const fetchedValue = fetchedSettings[key];
      matches[key] = JSON.stringify(savedValue) === JSON.stringify(fetchedValue);
    });

    testResults.tests[2].comparison.matches = matches;
    testResults.tests[2].status = Object.values(matches).every(match => match) ? 'success' : 'failed';

    return testResults;
  } catch (error) {
    testResults.error = error.message;
    testResults.tests.forEach(test => {
      if (test.status === 'attempting' || test.status === 'checking') {
        test.status = 'failed';
        test.error = error.message;
      }
    });
    return testResults;
  }
};

/**
 * Initialize settings sheet if it doesn't exist
 * @returns {Promise<boolean>} - Success status
 */
// COMMENTED OUT: Sheet creation functionality disabled for now
// export const initializeSettingsSheet = async () => {
//   try {
//     console.log('Initializing settings sheet...');

//     const response = await fetch(API_CONFIG.BASE_URL, {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify({
//         type: 'initialize_settings',
//         action: 'create_sheet'
//       })
//     });

//     if (!response.ok) {
//       throw new Error(`HTTP error! status: ${response.status}`);
//     }

//     const result = await response.json();
//     console.log('Settings sheet initialized:', result);

//     return true;
//   } catch (error) {
//     console.warn('Error initializing settings sheet:', error);
//     return false;
//   }
// };

// Temporary stub function while sheet creation is disabled
export const initializeSettingsSheet = async () => {
  // console.log('Sheet initialization is currently disabled');
  return true; // Return success to avoid breaking existing code
};
