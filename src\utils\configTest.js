// Configuration test utility
import { appConfig, isUsingSheets, isUsingAPI, isAuthEnabled, validateConfig } from '../config/appConfig';

/**
 * Test the current configuration
 */
export const testConfiguration = () => {
  // console.group('🔧 Configuration Test');

  // Basic configuration
  // console.log('Data Source:', appConfig.dataSource);
  // console.log('Using Sheets:', isUsingSheets());
  // console.log('Using API:', isUsingAPI());
  // console.log('Auth Enabled:', isAuthEnabled());

  // Validation
  const validation = validateConfig();
  // console.log('Configuration Valid:', validation.isValid);

  if (!validation.isValid) {
    // console.group('❌ Configuration Errors');
    // validation.errors.forEach(error => console.error(error));
    // console.groupEnd();
  }

  // Environment details
  // console.group('📋 Environment Details');
  // console.log('Environment:', appConfig.app.environment);
  // console.log('App Name:', appConfig.app.name);
  // console.log('Version:', appConfig.app.version);

  if (isUsingAPI()) {
    // console.log('API Base URL:', appConfig.api.baseUrl);
    // console.log('API Timeout:', appConfig.api.timeout);
  }

  if (isUsingSheets()) {
    // console.log('Sheets API Key:', appConfig.googleSheets.apiKey ? '✅ Set' : '❌ Missing');
    // console.log('Spreadsheet ID:', appConfig.googleSheets.spreadsheetId ? '✅ Set' : '❌ Missing');
  }

  // console.groupEnd();

  // Feature flags
  // console.group('🚩 Feature Flags');
  Object.entries(appConfig.features).forEach(([key, value]) => {
    // console.log(`${key}:`, value ? '✅ Enabled' : '❌ Disabled');
  });
  // console.groupEnd();

  // console.groupEnd();
  
  return {
    isValid: validation.isValid,
    errors: validation.errors,
    config: appConfig
  };
};

/**
 * Test data service connectivity
 */
export const testDataService = async () => {
  // console.group('🔗 Data Service Test');

  try {
    const { dataService } = await import('../services/dataService');

    // console.log('Testing data service connectivity...');

    // Test basic connectivity
    if (isUsingAPI()) {
      // console.log('Testing API connectivity...');
      // Try to fetch settings (usually a lightweight endpoint)
      try {
        await dataService.fetchSettings();
        // console.log('✅ API connection successful');
      } catch (error) {
        // console.error('❌ API connection failed:', error.message);
      }
    } else if (isUsingSheets()) {
      // console.log('Testing Google Sheets connectivity...');
      // Try to fetch contacts
      try {
        await dataService.fetchContacts();
        // console.log('✅ Google Sheets connection successful');
      } catch (error) {
        // console.error('❌ Google Sheets connection failed:', error.message);
      }
    }

    // Run comprehensive data service tests
    try {
      const { testDataServiceBasics } = await import('./testDataService');
      await testDataServiceBasics();
    } catch (error) {
      // console.warn('⚠️ Comprehensive data service tests failed:', error.message);
    }

  } catch (error) {
    // console.error('❌ Data service test failed:', error);
  }

  // console.groupEnd();
};

/**
 * Run all configuration tests
 */
export const runAllTests = async () => {
  // console.group('🧪 Running All Configuration Tests');
  
  const configTest = testConfiguration();
  
  if (configTest.isValid) {
    await testDataService();
  } else {
    // console.warn('⚠️ Skipping data service test due to configuration errors');
  }
  
  // console.groupEnd();
  
  return configTest;
};

// Auto-run tests in development mode
if (appConfig.app.environment === 'development' && appConfig.features.debugMode) {
  // Run tests after a short delay to allow other modules to load
  setTimeout(() => {
    runAllTests();
  }, 1000);
}

export default {
  testConfiguration,
  testDataService,
  runAllTests
};
