// Invoice number generation utilities
import dataService from '../services/dataService';

/**
 * Generate the next invoice number based on settings and existing invoices
 * @param {Object} settings - Application settings
 * @param {Array} existingInvoices - Array of existing invoices (optional)
 * @returns {Promise<string>} - The next invoice number
 */
export const generateNextInvoiceNumber = async (settings, existingInvoices = null) => {
  const {
    invoiceNumberFormat = 'prefix',
    invoicePrefix = 'INV-',
    invoiceStartingNumber = 1,
    invoiceNumberLength = 3
  } = settings;

  try {
    // Get existing invoices if not provided
    if (!existingInvoices) {
      existingInvoices = await dataService.fetchInvoices();
    }

    // Find the highest existing number
    let highestNumber = 0;

    if (existingInvoices && existingInvoices.length > 0) {
      existingInvoices.forEach(invoice => {
        if (invoice.invoiceNumber) {
          let numberPart;
          
          if (invoiceNumberFormat === 'prefix') {
            // Extract number from prefix format (e.g., "INV-001" -> 1)
            if (invoice.invoiceNumber.startsWith(invoicePrefix)) {
              numberPart = invoice.invoiceNumber.substring(invoicePrefix.length);
              numberPart = parseInt(numberPart, 10);
            }
          } else {
            // Simple number format
            numberPart = parseInt(invoice.invoiceNumber, 10);
          }

          if (!isNaN(numberPart) && numberPart > highestNumber) {
            highestNumber = numberPart;
          }
        }
      });
    }

    // Calculate next number
    const nextNumber = Math.max(highestNumber + 1, invoiceStartingNumber);

    // Format the invoice number based on settings
    if (invoiceNumberFormat === 'prefix') {
      // Prefix format with zero padding based on invoiceNumberLength setting
      return `${invoicePrefix}${String(nextNumber).padStart(invoiceNumberLength, '0')}`;
    } else {
      // Simple number format with zero padding based on invoiceNumberLength setting
      return String(nextNumber).padStart(invoiceNumberLength, '0');
    }

  } catch (error) {
    console.error('Error generating invoice number:', error);
    
    // Fallback to settings-based generation
    if (invoiceNumberFormat === 'prefix') {
      return `${invoicePrefix}${String(invoiceStartingNumber).padStart(invoiceNumberLength, '0')}`;
    } else {
      return String(invoiceStartingNumber).padStart(invoiceNumberLength, '0');
    }
  }
};

/**
 * Validate an invoice number format
 * @param {string} invoiceNumber - The invoice number to validate
 * @param {Object} settings - Application settings
 * @returns {boolean} - Whether the invoice number is valid
 */
export const validateInvoiceNumber = (invoiceNumber, settings) => {
  const {
    invoiceNumberFormat = 'prefix',
    invoicePrefix = 'INV-'
  } = settings;

  if (!invoiceNumber || typeof invoiceNumber !== 'string') {
    return false;
  }

  if (invoiceNumberFormat === 'prefix') {
    // Should start with prefix and have a number after
    if (!invoiceNumber.startsWith(invoicePrefix)) {
      return false;
    }
    
    const numberPart = invoiceNumber.substring(invoicePrefix.length);
    return /^\d+$/.test(numberPart);
  } else {
    // Should be a simple number
    return /^\d+$/.test(invoiceNumber);
  }
};

/**
 * Parse invoice number to extract the numeric part
 * @param {string} invoiceNumber - The invoice number to parse
 * @param {Object} settings - Application settings
 * @returns {number|null} - The numeric part or null if invalid
 */
export const parseInvoiceNumber = (invoiceNumber, settings) => {
  const {
    invoiceNumberFormat = 'prefix',
    invoicePrefix = 'INV-'
  } = settings;

  if (!invoiceNumber || typeof invoiceNumber !== 'string') {
    return null;
  }

  if (invoiceNumberFormat === 'prefix') {
    if (invoiceNumber.startsWith(invoicePrefix)) {
      const numberPart = invoiceNumber.substring(invoicePrefix.length);
      const parsed = parseInt(numberPart, 10);
      return isNaN(parsed) ? null : parsed;
    }
    return null;
  } else {
    const parsed = parseInt(invoiceNumber, 10);
    return isNaN(parsed) ? null : parsed;
  }
};

/**
 * Format a number as an invoice number based on settings
 * @param {number} number - The number to format
 * @param {Object} settings - Application settings
 * @returns {string} - The formatted invoice number
 */
export const formatInvoiceNumber = (number, settings) => {
  const {
    invoiceNumberFormat = 'prefix',
    invoicePrefix = 'INV-',
    invoiceNumberLength = 3
  } = settings;

  if (invoiceNumberFormat === 'prefix') {
    return `${invoicePrefix}${String(number).padStart(invoiceNumberLength, '0')}`;
  } else {
    return String(number).padStart(invoiceNumberLength, '0');
  }
};

/**
 * Get preview of next invoice number for display in settings
 * @param {Object} settings - Application settings
 * @returns {string} - Preview of next invoice number
 */
export const getInvoiceNumberPreview = (settings) => {
  const {
    invoiceNumberFormat = 'prefix',
    invoicePrefix = 'INV-',
    invoiceStartingNumber = 1,
    invoiceNumberLength = 3
  } = settings;

  if (invoiceNumberFormat === 'prefix') {
    return `${invoicePrefix}${String(invoiceStartingNumber).padStart(invoiceNumberLength, '0')}`;
  } else {
    return String(invoiceStartingNumber).padStart(invoiceNumberLength, '0');
  }
};

/**
 * Check if an invoice number already exists
 * @param {string} invoiceNumber - The invoice number to check
 * @param {Array} existingInvoices - Array of existing invoices
 * @returns {boolean} - Whether the invoice number exists
 */
export const invoiceNumberExists = (invoiceNumber, existingInvoices) => {
  if (!existingInvoices || !Array.isArray(existingInvoices)) {
    return false;
  }

  return existingInvoices.some(invoice => 
    invoice.invoiceNumber === invoiceNumber
  );
};

/**
 * Generate a unique invoice number that doesn't conflict with existing ones
 * @param {Object} settings - Application settings
 * @param {Array} existingInvoices - Array of existing invoices
 * @returns {Promise<string>} - A unique invoice number
 */
export const generateUniqueInvoiceNumber = async (settings, existingInvoices = null) => {
  let attempts = 0;
  const maxAttempts = 1000; // Prevent infinite loops

  while (attempts < maxAttempts) {
    const invoiceNumber = await generateNextInvoiceNumber(settings, existingInvoices);
    
    if (!invoiceNumberExists(invoiceNumber, existingInvoices)) {
      return invoiceNumber;
    }

    // If number exists, increment the starting number and try again
    settings = {
      ...settings,
      invoiceStartingNumber: (settings.invoiceStartingNumber || 1) + 1
    };
    
    attempts++;
  }

  // Fallback if we can't generate a unique number
  const timestamp = Date.now();
  if (settings.invoiceNumberFormat === 'prefix') {
    return `${settings.invoicePrefix || 'INV-'}${timestamp}`;
  } else {
    return String(timestamp);
  }
};

/**
 * Migrate existing invoice numbers to new format
 * @param {Array} invoices - Array of invoices to migrate
 * @param {Object} oldSettings - Previous settings
 * @param {Object} newSettings - New settings
 * @returns {Array} - Array of invoices with updated numbers
 */
export const migrateInvoiceNumbers = (invoices, oldSettings, newSettings) => {
  if (!invoices || !Array.isArray(invoices)) {
    return [];
  }

  // If format hasn't changed, no migration needed
  if (oldSettings.invoiceNumberFormat === newSettings.invoiceNumberFormat) {
    return invoices;
  }

  return invoices.map(invoice => {
    const numericPart = parseInvoiceNumber(invoice.invoiceNumber, oldSettings);
    
    if (numericPart !== null) {
      return {
        ...invoice,
        invoiceNumber: formatInvoiceNumber(numericPart, newSettings)
      };
    }

    // If we can't parse the old number, leave it as is
    return invoice;
  });
};
