// Performance optimization utilities for the Invoice Application
import React, { useState, useCallback, useMemo, useRef, useEffect, lazy, Suspense, memo } from 'react';
import ErrorBoundary from '../components/ui/ErrorBoundary';

/**
 * Debounce hook for delaying function execution
 * Useful for search inputs and API calls
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Throttle hook for limiting function execution frequency
 * Useful for scroll events and resize handlers
 */
export const useThrottle = (callback, delay) => {
  const lastRun = useRef(Date.now());

  return useCallback((...args) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = Date.now();
    }
  }, [callback, delay]);
};

/**
 * Memoized search filter for large lists
 * Optimizes filtering operations for contacts and products
 */
export const useSearchFilter = (items, searchTerm, searchFields) => {
  return useMemo(() => {
    if (!searchTerm || searchTerm.length < 2) {
      return items;
    }

    const lowercaseSearch = searchTerm.toLowerCase();
    
    return items.filter(item => {
      return searchFields.some(field => {
        const value = item[field];
        return value && value.toString().toLowerCase().includes(lowercaseSearch);
      });
    });
  }, [items, searchTerm, searchFields]);
};

/**
 * Virtual scrolling hook for large lists
 * Renders only visible items to improve performance
 */
export const useVirtualScroll = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 1,
      items.length
    );
    
    return {
      startIndex,
      endIndex,
      visibleItems: items.slice(startIndex, endIndex),
      totalHeight: items.length * itemHeight,
      offsetY: startIndex * itemHeight
    };
  }, [items, itemHeight, containerHeight, scrollTop]);

  const handleScroll = useCallback((e) => {
    setScrollTop(e.target.scrollTop);
  }, []);

  return {
    ...visibleItems,
    handleScroll
  };
};

/**
 * Intersection Observer hook for lazy loading
 * Useful for images and components that should load when visible
 */
export const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const targetRef = useRef(null);

  useEffect(() => {
    const target = targetRef.current;
    if (!target) return;

    const observer = new IntersectionObserver(([entry]) => {
      setIsIntersecting(entry.isIntersecting);
      if (entry.isIntersecting && !hasIntersected) {
        setHasIntersected(true);
      }
    }, options);

    observer.observe(target);

    return () => {
      observer.unobserve(target);
    };
  }, [options, hasIntersected]);

  return { targetRef, isIntersecting, hasIntersected };
};

/**
 * Memoized calculation hook
 * Prevents expensive calculations from running on every render
 */
export const useMemoizedCalculation = (calculationFn, dependencies) => {
  return useMemo(() => {
    const startTime = performance.now();
    const result = calculationFn();
    const endTime = performance.now();
    
    // Log slow calculations in development
    if (process.env.NODE_ENV === 'development' && endTime - startTime > 10) {
      // console.warn(`Slow calculation detected: ${endTime - startTime}ms`);
    }
    
    return result;
  }, dependencies);
};

/**
 * Optimized event handler hook
 * Prevents unnecessary re-renders by memoizing event handlers
 */
export const useOptimizedCallback = (callback, dependencies) => {
  return useCallback(callback, dependencies);
};

/**
 * Performance monitoring hook
 * Tracks component render times and re-render frequency
 */
export const usePerformanceMonitor = (componentName) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    const currentTime = Date.now();
    const timeSinceLastRender = currentTime - lastRenderTime.current;
    
    if (process.env.NODE_ENV === 'development') {
      // console.log(`${componentName} rendered ${renderCount.current} times. Time since last render: ${timeSinceLastRender}ms`);
    }
    
    lastRenderTime.current = currentTime;
  });

  return {
    renderCount: renderCount.current,
    timeSinceLastRender: Date.now() - lastRenderTime.current
  };
};

/**
 * Lazy image loading component
 * Loads images only when they come into view
 */
export const LazyImage = ({ src, alt, className, style, placeholder }) => {
  const { targetRef, hasIntersected } = useIntersectionObserver({
    threshold: 0.1,
    rootMargin: '50px'
  });

  return (
    <div ref={targetRef} className={className} style={style}>
      {hasIntersected ? (
        <img src={src} alt={alt} style={{ width: '100%', height: '100%' }} />
      ) : (
        <div style={{ 
          width: '100%', 
          height: '100%', 
          backgroundColor: '#f3f4f6',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#6b7280'
        }}>
          {placeholder || 'Loading...'}
        </div>
      )}
    </div>
  );
};

/**
 * Bundle size analyzer utility
 * Helps identify large dependencies and optimization opportunities
 */
export const analyzeBundleSize = () => {
  if (process.env.NODE_ENV === 'development') {
    // This would typically be used with webpack-bundle-analyzer
    // console.log('Bundle analysis would run here in development mode');
  }
};

/**
 * Memory usage monitor
 * Tracks memory usage and warns about potential leaks
 */
export const useMemoryMonitor = () => {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && 'memory' in performance) {
      const checkMemory = () => {
        const memory = performance.memory;
        const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
        const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
        
        if (usedMB > 50) { // Warn if using more than 50MB
          // console.warn(`High memory usage detected: ${usedMB}MB / ${totalMB}MB`);
        }
      };

      const interval = setInterval(checkMemory, 10000); // Check every 10 seconds
      return () => clearInterval(interval);
    }
  }, []);
};

/**
 * Component size optimizer
 * Provides utilities for optimizing component bundle size
 */
export const ComponentOptimizer = {
  /**
   * Lazy load a component with error boundary
   */
  lazyWithErrorBoundary: (importFn, fallback = null) => {
    const LazyComponent = lazy(importFn);
    
    return (props) => (
      <ErrorBoundary fallback={<div>Error loading component</div>}>
        <Suspense fallback={fallback}>
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  },

  /**
   * Create a memoized component with custom comparison
   */
  memoWithComparison: (Component, compareProps) => {
    return memo(Component, compareProps);
  },

  /**
   * Optimize props for memo components
   */
  optimizeProps: (props) => {
    // Remove functions and objects that change on every render
    const optimized = {};
    for (const [key, value] of Object.entries(props)) {
      if (typeof value !== 'function' && typeof value !== 'object') {
        optimized[key] = value;
      }
    }
    return optimized;
  }
};

/**
 * Performance metrics collection
 */
export const PerformanceMetrics = {
  /**
   * Measure component render time
   */
  measureRender: (componentName, renderFn) => {
    const startTime = performance.now();
    const result = renderFn();
    const endTime = performance.now();
    
    if (process.env.NODE_ENV === 'development') {
      // console.log(`${componentName} render time: ${endTime - startTime}ms`);
    }
    
    return result;
  },

  /**
   * Track user interactions
   */
  trackInteraction: (action, data = {}) => {
    if (process.env.NODE_ENV === 'development') {
      // console.log(`User interaction: ${action}`, data);
    }
    
    // In production, this would send to analytics
  },

  /**
   * Monitor Core Web Vitals
   */
  monitorWebVitals: () => {
    if ('web-vitals' in window) {
      // This would integrate with web-vitals library
      // console.log('Web Vitals monitoring would be initialized here');
    }
  }
};
